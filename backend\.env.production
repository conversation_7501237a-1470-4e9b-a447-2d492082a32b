# Production Environment Configuration
# Copy this file to .env and update with your production values

# Application
NODE_ENV=production
APP_VERSION=1.0.0
PORT=3001
HOST=0.0.0.0

# Database
MONGODB_URI=*******************************************************************************************

# Redis Cache (optional)
REDIS_URL=redis://:your-redis-password@localhost:6379

# JWT Configuration
JWT_SECRET=your-super-secure-jwt-secret-key-at-least-32-characters-long
JWT_REFRESH_SECRET=your-super-secure-refresh-secret-key-at-least-32-characters-long
JWT_EXPIRES_IN=15m
JWT_REFRESH_EXPIRES_IN=7d

# Security
CORS_ORIGIN=https://yourdomain.com,https://www.yourdomain.com
CORS_CREDENTIALS=true
TRUST_PROXY=1

# Rate Limiting
RATE_LIMIT_WINDOW=15
RATE_LIMIT_MAX=100

# File Upload
MAX_FILE_SIZE=10485760

# Email Service (configure based on your email provider)
EMAIL_SERVICE=gmail
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-app-password
EMAIL_FROM=<EMAIL>

# Feature Flags
FEATURE_RATE_LIMITING=true
FEATURE_REAL_TIME_UPDATES=true
FEATURE_AUDIT_LOGGING=true
FEATURE_API_DOCS=false

# Logging
LOG_LEVEL=info

# SSL/TLS (if using HTTPS)
SSL_CERT_PATH=/path/to/ssl/cert.pem
SSL_KEY_PATH=/path/to/ssl/key.pem

# External Services
# Add your external service configurations here
# PAYMENT_GATEWAY_API_KEY=
# NOTIFICATION_SERVICE_URL=
# ANALYTICS_SERVICE_KEY=
