import React, { useState } from 'react';
import DashboardLayout from '../../layouts/DashboardLayout';
import { BarChart3Icon, TrendingUpIcon, CalendarIcon, DownloadIcon, FilterIcon } from 'lucide-react';
import Button from '../../components/ui/Button';
const GlobalAnalytics: React.FC = () => {
  const [timeframe, setTimeframe] = useState('month');
  const [dataView, setDataView] = useState('revenue');
  // Sample analytics data
  const revenueData = [{
    month: 'Jan',
    value: 1200000
  }, {
    month: 'Feb',
    value: 1450000
  }, {
    month: 'Mar',
    value: 1320000
  }, {
    month: 'Apr',
    value: 1680000
  }, {
    month: 'May',
    value: 1550000
  }, {
    month: 'Jun',
    value: 1720000
  }, {
    month: 'Jul',
    value: 1840000
  }, {
    month: 'Aug',
    value: 1920000
  }, {
    month: 'Sep',
    value: 2050000
  }, {
    month: 'Oct',
    value: 1980000
  }, {
    month: 'Nov',
    value: 2150000
  }, {
    month: 'Dec',
    value: 2280000
  }];
  const topPerformers = [{
    company: 'TechVest Inc.',
    performance: 26.3,
    status: 'Increasing'
  }, {
    company: 'MediLife Ventures',
    performance: 22.1,
    status: 'Increasing'
  }, {
    company: 'GreenEnergy Fund',
    performance: 18.7,
    status: 'Stable'
  }, {
    company: 'Global Finance Group',
    performance: 15.2,
    status: 'Decreasing'
  }, {
    company: 'Retail Holdings',
    performance: 12.8,
    status: 'Stable'
  }];
  const riskAssessment = [{
    category: 'Market Volatility',
    score: 68,
    status: 'Medium'
  }, {
    category: 'Liquidity Risk',
    score: 42,
    status: 'Low'
  }, {
    category: 'Credit Risk',
    score: 73,
    status: 'Medium'
  }, {
    category: 'Operational Risk',
    score: 51,
    status: 'Medium'
  }, {
    category: 'Regulatory Risk',
    score: 35,
    status: 'Low'
  }];
  const getBarHeight = (value: number) => {
    const maxValue = Math.max(...revenueData.map(item => item.value));
    return value / maxValue * 100;
  };
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Increasing':
        return 'text-green-500';
      case 'Decreasing':
        return 'text-red-500';
      default:
        return 'text-yellow-500';
    }
  };
  const getRiskColor = (status: string) => {
    switch (status) {
      case 'High':
        return 'bg-red-500';
      case 'Medium':
        return 'bg-yellow-500';
      default:
        return 'bg-green-500';
    }
  };
  return <DashboardLayout title="Global Analytics" subtitle="Platform-wide performance metrics and insights">
      {/* Controls */}
      <div className="flex flex-wrap gap-4 mb-6 justify-between">
        <div className="flex flex-wrap gap-2">
          <Button variant={timeframe === 'month' ? 'primary' : 'secondary'} onClick={() => setTimeframe('month')} className="flex items-center">
            <CalendarIcon className="h-4 w-4 mr-2" />
            Monthly
          </Button>
          <Button variant={timeframe === 'quarter' ? 'primary' : 'secondary'} onClick={() => setTimeframe('quarter')} className="flex items-center">
            <CalendarIcon className="h-4 w-4 mr-2" />
            Quarterly
          </Button>
          <Button variant={timeframe === 'year' ? 'primary' : 'secondary'} onClick={() => setTimeframe('year')} className="flex items-center">
            <CalendarIcon className="h-4 w-4 mr-2" />
            Yearly
          </Button>
        </div>
        <Button variant="secondary" className="flex items-center">
          <DownloadIcon className="h-4 w-4 mr-2" />
          Export Report
        </Button>
      </div>
      {/* Data View Tabs */}
      <div className="bg-slate-800 rounded-lg overflow-hidden mb-6">
        <div className="border-b border-slate-700">
          <div className="flex overflow-x-auto">
            <button className={`px-6 py-4 text-sm font-medium ${dataView === 'revenue' ? 'text-yellow-500 border-b-2 border-yellow-500' : 'text-slate-400 hover:text-white'}`} onClick={() => setDataView('revenue')}>
              <BarChart3Icon className="h-4 w-4 inline mr-2" />
              Revenue
            </button>
            <button className={`px-6 py-4 text-sm font-medium ${dataView === 'performance' ? 'text-yellow-500 border-b-2 border-yellow-500' : 'text-slate-400 hover:text-white'}`} onClick={() => setDataView('performance')}>
              <TrendingUpIcon className="h-4 w-4 inline mr-2" />
              Performance
            </button>
            <button className={`px-6 py-4 text-sm font-medium ${dataView === 'risk' ? 'text-yellow-500 border-b-2 border-yellow-500' : 'text-slate-400 hover:text-white'}`} onClick={() => setDataView('risk')}>
              <FilterIcon className="h-4 w-4 inline mr-2" />
              Risk Assessment
            </button>
          </div>
        </div>
        <div className="p-6">
          {dataView === 'revenue' && <>
              <h3 className="text-lg font-medium text-white mb-4">
                Revenue Trends
              </h3>
              <div className="h-64 flex items-end space-x-4">
                {revenueData.map((item, index) => <div key={index} className="flex flex-col items-center flex-1">
                    <div className="w-full bg-slate-700 rounded-t-sm relative" style={{
                height: `${getBarHeight(item.value)}%`
              }}>
                      <div className="absolute inset-x-0 bottom-0 h-full bg-gradient-to-t from-yellow-500/80 to-yellow-500/20 rounded-t-sm"></div>
                    </div>
                    <div className="text-xs text-slate-400 mt-2">
                      {item.month}
                    </div>
                  </div>)}
              </div>
              <div className="mt-4 text-center">
                <div className="text-2xl font-bold text-white">
                  $
                  {(revenueData.reduce((sum, item) => sum + item.value, 0) / 1000000).toFixed(2)}
                  M
                </div>
                <div className="text-sm text-slate-400">Total Revenue</div>
              </div>
            </>}
          {dataView === 'performance' && <>
              <h3 className="text-lg font-medium text-white mb-4">
                Top Performing Sub-Companies
              </h3>
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="text-left text-sm text-slate-400 border-b border-slate-700">
                      <th className="pb-2 font-medium">Company</th>
                      <th className="pb-2 font-medium">ROI</th>
                      <th className="pb-2 font-medium">Trend</th>
                      <th className="pb-2 font-medium">Status</th>
                    </tr>
                  </thead>
                  <tbody>
                    {topPerformers.map((company, index) => <tr key={index} className="border-b border-slate-700">
                        <td className="py-3 text-white">{company.company}</td>
                        <td className="py-3 text-white">
                          {company.performance}%
                        </td>
                        <td className="py-3">
                          <div className="w-32 h-2 bg-slate-700 rounded-full overflow-hidden">
                            <div className={`h-full ${company.status === 'Increasing' ? 'bg-green-500' : company.status === 'Decreasing' ? 'bg-red-500' : 'bg-yellow-500'}`} style={{
                        width: `${company.performance * 3}%`
                      }}></div>
                          </div>
                        </td>
                        <td className={`py-3 ${getStatusColor(company.status)}`}>
                          {company.status}
                        </td>
                      </tr>)}
                  </tbody>
                </table>
              </div>
            </>}
          {dataView === 'risk' && <>
              <h3 className="text-lg font-medium text-white mb-4">
                Risk Assessment
              </h3>
              <div className="space-y-6">
                {riskAssessment.map((risk, index) => <div key={index}>
                    <div className="flex justify-between mb-1">
                      <span className="text-sm text-slate-400">
                        {risk.category}
                      </span>
                      <span className="text-sm text-white">
                        {risk.score}/100
                      </span>
                    </div>
                    <div className="w-full h-2 bg-slate-700 rounded-full overflow-hidden">
                      <div className={getRiskColor(risk.status)} style={{
                  width: `${risk.score}%`
                }}></div>
                    </div>
                    <div className="text-xs text-right mt-1">
                      <span className={`px-2 py-0.5 rounded-full ${risk.status === 'High' ? 'bg-red-500/20 text-red-500' : risk.status === 'Medium' ? 'bg-yellow-500/20 text-yellow-500' : 'bg-green-500/20 text-green-500'}`}>
                        {risk.status} Risk
                      </span>
                    </div>
                  </div>)}
              </div>
            </>}
        </div>
      </div>
      {/* Additional Analytics */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="bg-slate-800 p-6 rounded-lg">
          <h3 className="text-lg font-medium text-white mb-4">
            Investment Distribution
          </h3>
          <div className="space-y-4">
            <div>
              <div className="flex justify-between mb-1">
                <span className="text-sm text-slate-400">Technology</span>
                <span className="text-sm text-white">45%</span>
              </div>
              <div className="w-full h-2 bg-slate-700 rounded-full overflow-hidden">
                <div className="bg-blue-500 h-full" style={{
                width: '45%'
              }}></div>
              </div>
            </div>
            <div>
              <div className="flex justify-between mb-1">
                <span className="text-sm text-slate-400">Healthcare</span>
                <span className="text-sm text-white">28%</span>
              </div>
              <div className="w-full h-2 bg-slate-700 rounded-full overflow-hidden">
                <div className="bg-green-500 h-full" style={{
                width: '28%'
              }}></div>
              </div>
            </div>
            <div>
              <div className="flex justify-between mb-1">
                <span className="text-sm text-slate-400">Finance</span>
                <span className="text-sm text-white">15%</span>
              </div>
              <div className="w-full h-2 bg-slate-700 rounded-full overflow-hidden">
                <div className="bg-yellow-500 h-full" style={{
                width: '15%'
              }}></div>
              </div>
            </div>
            <div>
              <div className="flex justify-between mb-1">
                <span className="text-sm text-slate-400">Retail</span>
                <span className="text-sm text-white">12%</span>
              </div>
              <div className="w-full h-2 bg-slate-700 rounded-full overflow-hidden">
                <div className="bg-purple-500 h-full" style={{
                width: '12%'
              }}></div>
              </div>
            </div>
          </div>
        </div>
        <div className="bg-slate-800 p-6 rounded-lg">
          <h3 className="text-lg font-medium text-white mb-4">
            Performance Metrics
          </h3>
          <div className="grid grid-cols-2 gap-4">
            <div className="bg-slate-700 p-4 rounded-lg text-center">
              <div className="text-2xl font-bold text-yellow-500">19.8%</div>
              <div className="text-sm text-slate-400">Avg. ROI</div>
            </div>
            <div className="bg-slate-700 p-4 rounded-lg text-center">
              <div className="text-2xl font-bold text-green-500">$8.2M</div>
              <div className="text-sm text-slate-400">Net Profit</div>
            </div>
            <div className="bg-slate-700 p-4 rounded-lg text-center">
              <div className="text-2xl font-bold text-blue-500">87%</div>
              <div className="text-sm text-slate-400">Success Rate</div>
            </div>
            <div className="bg-slate-700 p-4 rounded-lg text-center">
              <div className="text-2xl font-bold text-purple-500">$42M</div>
              <div className="text-sm text-slate-400">Total AUM</div>
            </div>
          </div>
        </div>
        <div className="bg-slate-800 p-6 rounded-lg">
          <h3 className="text-lg font-medium text-white mb-4">
            Upcoming Events
          </h3>
          <div className="space-y-4">
            <div className="bg-slate-700 p-3 rounded-lg">
              <div className="flex justify-between">
                <div className="text-sm font-medium text-white">
                  Quarterly Review
                </div>
                <div className="text-xs text-slate-400">Jul 15</div>
              </div>
              <div className="text-xs text-slate-400 mt-1">
                All company performance review
              </div>
            </div>
            <div className="bg-slate-700 p-3 rounded-lg">
              <div className="flex justify-between">
                <div className="text-sm font-medium text-white">
                  Board Meeting
                </div>
                <div className="text-xs text-slate-400">Jul 22</div>
              </div>
              <div className="text-xs text-slate-400 mt-1">
                Annual strategy planning
              </div>
            </div>
            <div className="bg-slate-700 p-3 rounded-lg">
              <div className="flex justify-between">
                <div className="text-sm font-medium text-white">
                  Investor Conference
                </div>
                <div className="text-xs text-slate-400">Aug 5</div>
              </div>
              <div className="text-xs text-slate-400 mt-1">
                Present new investment opportunities
              </div>
            </div>
          </div>
        </div>
      </div>
    </DashboardLayout>;
};
export default GlobalAnalytics;