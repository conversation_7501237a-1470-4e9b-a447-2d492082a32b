import React, { useState } from 'react';
import { SearchIcon, FilterIcon, StarIcon } from 'lucide-react';
import DashboardLayout from '../../layouts/DashboardLayout';
import Button from '../../components/ui/Button';
const Marketplace: React.FC = () => {
  const [filter, setFilter] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');
  // Sample investment opportunities
  const opportunities = [{
    id: 'opp-001',
    name: 'Tech Growth Fund Series C',
    description: 'High-growth technology companies with proven business models and strong market positions.',
    minInvestment: 10000,
    targetReturn: 22,
    riskLevel: 'medium',
    category: 'Technology',
    duration: '36 months',
    remaining: '65%',
    featured: true
  }, {
    id: 'opp-002',
    name: 'Healthcare Innovation Fund',
    description: 'Cutting-edge healthcare startups focused on digital health solutions and medical devices.',
    minInvestment: 25000,
    targetReturn: 18,
    riskLevel: 'medium',
    category: 'Healthcare',
    duration: '48 months',
    remaining: '42%',
    featured: false
  }, {
    id: 'opp-003',
    name: 'Renewable Energy Project',
    description: 'Solar and wind energy projects with long-term power purchase agreements.',
    minInvestment: 15000,
    targetReturn: 12,
    riskLevel: 'low',
    category: 'Energy',
    duration: '60 months',
    remaining: '28%',
    featured: false
  }, {
    id: 'opp-004',
    name: 'Commercial Real Estate Portfolio',
    description: 'Diversified portfolio of grade-A commercial properties in prime locations.',
    minInvestment: 50000,
    targetReturn: 15,
    riskLevel: 'low',
    category: 'Real Estate',
    duration: '72 months',
    remaining: '53%',
    featured: true
  }, {
    id: 'opp-005',
    name: 'Fintech Startup Accelerator',
    description: 'Early-stage fintech startups with disruptive business models and strong founding teams.',
    minInvestment: 20000,
    targetReturn: 28,
    riskLevel: 'high',
    category: 'Finance',
    duration: '36 months',
    remaining: '18%',
    featured: false
  }, {
    id: 'opp-006',
    name: 'E-commerce Growth Fund',
    description: 'Established e-commerce businesses with proven revenue models and expansion plans.',
    minInvestment: 15000,
    targetReturn: 20,
    riskLevel: 'medium',
    category: 'Retail',
    duration: '36 months',
    remaining: '75%',
    featured: false
  }];
  const filteredOpportunities = opportunities.filter(opportunity => {
    if (filter === 'all') return true;
    return opportunity.category.toLowerCase() === filter.toLowerCase();
  }).filter(opportunity => opportunity.name.toLowerCase().includes(searchTerm.toLowerCase()) || opportunity.description.toLowerCase().includes(searchTerm.toLowerCase()) || opportunity.category.toLowerCase().includes(searchTerm.toLowerCase()));
  const getRiskColor = (risk: string) => {
    switch (risk) {
      case 'high':
        return 'bg-red-500/20 text-red-500';
      case 'medium':
        return 'bg-yellow-500/20 text-yellow-500';
      case 'low':
        return 'bg-green-500/20 text-green-500';
      default:
        return 'bg-slate-500/20 text-slate-500';
    }
  };
  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'Technology':
        return 'bg-blue-500/20 text-blue-500';
      case 'Healthcare':
        return 'bg-green-500/20 text-green-500';
      case 'Energy':
        return 'bg-yellow-500/20 text-yellow-500';
      case 'Real Estate':
        return 'bg-purple-500/20 text-purple-500';
      case 'Finance':
        return 'bg-teal-500/20 text-teal-500';
      case 'Retail':
        return 'bg-pink-500/20 text-pink-500';
      default:
        return 'bg-slate-500/20 text-slate-500';
    }
  };
  return <DashboardLayout title="Investment Marketplace" subtitle="Discover and invest in new opportunities">
      <div className="mb-6 flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <div className="flex items-center gap-4">
          <div className="relative">
            <input type="text" placeholder="Search opportunities..." value={searchTerm} onChange={e => setSearchTerm(e.target.value)} className="w-full md:w-64 bg-slate-700 border border-slate-600 rounded-md py-2 pl-10 pr-4 text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-yellow-500" />
            <SearchIcon className="absolute left-3 top-2.5 h-5 w-5 text-slate-400" />
          </div>
          <div className="relative">
            <select value={filter} onChange={e => setFilter(e.target.value)} className="appearance-none bg-slate-700 border border-slate-600 rounded-md py-2 pl-4 pr-10 text-white focus:outline-none focus:ring-2 focus:ring-yellow-500">
              <option value="all">All Categories</option>
              <option value="technology">Technology</option>
              <option value="healthcare">Healthcare</option>
              <option value="energy">Energy</option>
              <option value="real estate">Real Estate</option>
              <option value="finance">Finance</option>
              <option value="retail">Retail</option>
            </select>
            <FilterIcon className="absolute right-3 top-2.5 h-5 w-5 text-slate-400 pointer-events-none" />
          </div>
        </div>
      </div>
      {/* Featured Opportunities */}
      <div className="mb-8">
        <h2 className="text-xl font-semibold text-white mb-4">
          Featured Opportunities
        </h2>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {opportunities.filter(opp => opp.featured).map(opportunity => <div key={opportunity.id} className="bg-slate-800 rounded-lg overflow-hidden border border-yellow-500/30">
                <div className="p-6">
                  <div className="flex justify-between items-start mb-4">
                    <div>
                      <div className="flex items-center">
                        <h3 className="text-lg font-semibold text-white">
                          {opportunity.name}
                        </h3>
                        <StarIcon className="h-5 w-5 text-yellow-500 ml-2 fill-yellow-500" />
                      </div>
                      <span className={`mt-2 inline-block px-2 py-1 rounded-full text-xs font-medium ${getCategoryColor(opportunity.category)}`}>
                        {opportunity.category}
                      </span>
                    </div>
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${getRiskColor(opportunity.riskLevel)}`}>
                      {opportunity.riskLevel.charAt(0).toUpperCase() + opportunity.riskLevel.slice(1)}{' '}
                      Risk
                    </span>
                  </div>
                  <p className="text-slate-400 mb-4">
                    {opportunity.description}
                  </p>
                  <div className="grid grid-cols-2 gap-4 mb-4">
                    <div>
                      <p className="text-xs text-slate-400">Min. Investment</p>
                      <p className="text-white font-medium">
                        ${opportunity.minInvestment.toLocaleString()}
                      </p>
                    </div>
                    <div>
                      <p className="text-xs text-slate-400">Target Return</p>
                      <p className="text-yellow-500 font-medium">
                        {opportunity.targetReturn}%
                      </p>
                    </div>
                    <div>
                      <p className="text-xs text-slate-400">Duration</p>
                      <p className="text-white font-medium">
                        {opportunity.duration}
                      </p>
                    </div>
                    <div>
                      <p className="text-xs text-slate-400">Remaining</p>
                      <p className="text-white font-medium">
                        {opportunity.remaining}
                      </p>
                    </div>
                  </div>
                  <Button variant="primary" fullWidth>
                    View Details
                  </Button>
                </div>
              </div>)}
        </div>
      </div>
      {/* All Opportunities */}
      <h2 className="text-xl font-semibold text-white mb-4">
        All Opportunities
      </h2>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredOpportunities.map(opportunity => <div key={opportunity.id} className="bg-slate-800 rounded-lg overflow-hidden">
            <div className="p-6">
              <div className="flex justify-between items-start mb-4">
                <div>
                  <h3 className="text-lg font-semibold text-white">
                    {opportunity.name}
                  </h3>
                  <span className={`mt-2 inline-block px-2 py-1 rounded-full text-xs font-medium ${getCategoryColor(opportunity.category)}`}>
                    {opportunity.category}
                  </span>
                </div>
                <span className={`px-2 py-1 rounded-full text-xs font-medium ${getRiskColor(opportunity.riskLevel)}`}>
                  {opportunity.riskLevel.charAt(0).toUpperCase() + opportunity.riskLevel.slice(1)}{' '}
                  Risk
                </span>
              </div>
              <p className="text-slate-400 mb-4 line-clamp-2">
                {opportunity.description}
              </p>
              <div className="grid grid-cols-2 gap-4 mb-4">
                <div>
                  <p className="text-xs text-slate-400">Min. Investment</p>
                  <p className="text-white font-medium">
                    ${opportunity.minInvestment.toLocaleString()}
                  </p>
                </div>
                <div>
                  <p className="text-xs text-slate-400">Target Return</p>
                  <p className="text-yellow-500 font-medium">
                    {opportunity.targetReturn}%
                  </p>
                </div>
                <div>
                  <p className="text-xs text-slate-400">Duration</p>
                  <p className="text-white font-medium">
                    {opportunity.duration}
                  </p>
                </div>
                <div>
                  <p className="text-xs text-slate-400">Remaining</p>
                  <p className="text-white font-medium">
                    {opportunity.remaining}
                  </p>
                </div>
              </div>
              <Button variant="secondary" fullWidth>
                View Details
              </Button>
            </div>
          </div>)}
        {filteredOpportunities.length === 0 && <div className="col-span-full bg-slate-800 p-8 rounded-lg text-center">
            <p className="text-slate-400">
              No investment opportunities found matching your criteria
            </p>
          </div>}
      </div>
    </DashboardLayout>;
};
export default Marketplace;