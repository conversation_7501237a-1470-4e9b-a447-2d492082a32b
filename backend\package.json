{"name": "investment-management-backend", "version": "1.0.0", "description": "Backend API for Investment Management System", "main": "server.js", "type": "module", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["investment", "management", "api", "mongodb", "express"], "author": "Investment Management Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "mongoose": "^8.16.0", "cors": "^2.8.5", "dotenv": "^16.3.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "express-validator": "^7.0.1", "helmet": "^7.1.0", "express-rate-limit": "^7.1.5", "uuid": "^11.1.0", "date-fns": "^4.1.0"}, "devDependencies": {"nodemon": "^3.0.2", "@types/node": "^20.11.18"}}