import React, { useState, createContext, useContext } from 'react';
interface MarketData {
  stockMarketIndex: number;
  stockMarketChange: number;
  cryptoMarketIndex: number;
  cryptoMarketChange: number;
  realEstateIndex: number;
  realEstateChange: number;
}
interface MarketDataContextType {
  marketData: MarketData | null;
  loading: boolean;
  fetchMarketData: () => Promise<void>;
}
const MarketDataContext = createContext<MarketDataContextType>({
  marketData: null,
  loading: false,
  fetchMarketData: async () => {}
});
export const useMarketData = () => useContext(MarketDataContext);
export const MarketDataProvider: React.FC<{
  children: React.ReactNode;
}> = ({
  children
}) => {
  const [marketData, setMarketData] = useState<MarketData | null>(null);
  const [loading, setLoading] = useState(false);
  const fetchMarketData = async () => {
    setLoading(true);
    try {
      // This would be an API call in a real application
      // Simulating API response for demo
      const mockMarketData: MarketData = {
        stockMarketIndex: 4721.32,
        stockMarketChange: 1.2,
        cryptoMarketIndex: 2843.18,
        cryptoMarketChange: -2.8,
        realEstateIndex: 1843.67,
        realEstateChange: 0.5
      };
      setMarketData(mockMarketData);
    } catch (error) {
      console.error('Failed to fetch market data:', error);
    } finally {
      setLoading(false);
    }
  };
  return <MarketDataContext.Provider value={{
    marketData,
    loading,
    fetchMarketData
  }}>
      {children}
    </MarketDataContext.Provider>;
};