import React, { useState } from 'react';
import { PlusIcon, FilterIcon, SearchIcon, TrendingUpIcon, TrendingDownIcon } from 'lucide-react';
import DashboardLayout from '../../layouts/DashboardLayout';
import Button from '../../components/ui/Button';
const Investments: React.FC = () => {
  const [filter, setFilter] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');
  // Sample investments data
  const investments = [{
    id: 'inv-001',
    name: 'Tech Growth Fund',
    amount: 250000,
    roi: 18.5,
    status: 'active',
    type: 'equity',
    date: '2023-03-15',
    duration: '36 months',
    risk: 'medium'
  }, {
    id: 'inv-002',
    name: 'Healthcare Innovation',
    amount: 180000,
    roi: 12.3,
    status: 'active',
    type: 'venture',
    date: '2023-02-08',
    duration: '48 months',
    risk: 'high'
  }, {
    id: 'inv-003',
    name: 'Renewable Energy Project',
    amount: 320000,
    roi: 9.7,
    status: 'pending',
    type: 'project',
    date: '2023-04-22',
    duration: '60 months',
    risk: 'medium'
  }, {
    id: 'inv-004',
    name: 'Real Estate Development',
    amount: 450000,
    roi: 7.2,
    status: 'completed',
    type: 'real estate',
    date: '2022-11-30',
    duration: '24 months',
    risk: 'low'
  }, {
    id: 'inv-005',
    name: 'Fintech Startup',
    amount: 150000,
    roi: 22.8,
    status: 'active',
    type: 'seed',
    date: '2023-01-12',
    duration: '36 months',
    risk: 'high'
  }];
  const filteredInvestments = investments.filter(investment => {
    if (filter === 'all') return true;
    return investment.status === filter;
  }).filter(investment => investment.name.toLowerCase().includes(searchTerm.toLowerCase()) || investment.type.toLowerCase().includes(searchTerm.toLowerCase()));
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-500/20 text-green-500';
      case 'pending':
        return 'bg-yellow-500/20 text-yellow-500';
      case 'completed':
        return 'bg-blue-500/20 text-blue-500';
      default:
        return 'bg-slate-500/20 text-slate-500';
    }
  };
  const getRiskColor = (risk: string) => {
    switch (risk) {
      case 'high':
        return 'text-red-500';
      case 'medium':
        return 'text-yellow-500';
      case 'low':
        return 'text-green-500';
      default:
        return 'text-slate-500';
    }
  };
  return <DashboardLayout title="Investments" subtitle="Manage your company's investment portfolio">
      <div className="mb-6 flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <div className="flex items-center gap-4">
          <div className="relative">
            <input type="text" placeholder="Search investments..." value={searchTerm} onChange={e => setSearchTerm(e.target.value)} className="w-full md:w-64 bg-slate-700 border border-slate-600 rounded-md py-2 pl-10 pr-4 text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-yellow-500" />
            <SearchIcon className="absolute left-3 top-2.5 h-5 w-5 text-slate-400" />
          </div>
          <div className="relative">
            <select value={filter} onChange={e => setFilter(e.target.value)} className="appearance-none bg-slate-700 border border-slate-600 rounded-md py-2 pl-4 pr-10 text-white focus:outline-none focus:ring-2 focus:ring-yellow-500">
              <option value="all">All Status</option>
              <option value="active">Active</option>
              <option value="pending">Pending</option>
              <option value="completed">Completed</option>
            </select>
            <FilterIcon className="absolute right-3 top-2.5 h-5 w-5 text-slate-400 pointer-events-none" />
          </div>
        </div>
        <Button variant="primary" className="flex items-center justify-center">
          <PlusIcon className="h-5 w-5 mr-2" />
          New Investment
        </Button>
      </div>
      <div className="bg-slate-800 rounded-lg overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="text-left text-sm text-slate-400 border-b border-slate-700">
                <th className="p-4 font-medium">Name</th>
                <th className="p-4 font-medium">Amount</th>
                <th className="p-4 font-medium">ROI</th>
                <th className="p-4 font-medium">Type</th>
                <th className="p-4 font-medium">Date</th>
                <th className="p-4 font-medium">Duration</th>
                <th className="p-4 font-medium">Risk</th>
                <th className="p-4 font-medium">Status</th>
                <th className="p-4 font-medium">Actions</th>
              </tr>
            </thead>
            <tbody>
              {filteredInvestments.map(investment => <tr key={investment.id} className="border-b border-slate-700 hover:bg-slate-700/50">
                  <td className="p-4 text-white font-medium">
                    {investment.name}
                  </td>
                  <td className="p-4 text-white">
                    ${investment.amount.toLocaleString()}
                  </td>
                  <td className="p-4">
                    <div className="flex items-center">
                      {investment.roi > 15 ? <TrendingUpIcon className="h-4 w-4 text-green-500 mr-1" /> : investment.roi < 10 ? <TrendingDownIcon className="h-4 w-4 text-red-500 mr-1" /> : <TrendingUpIcon className="h-4 w-4 text-yellow-500 mr-1" />}
                      <span className={investment.roi > 15 ? 'text-green-500' : investment.roi < 10 ? 'text-red-500' : 'text-yellow-500'}>
                        {investment.roi}%
                      </span>
                    </div>
                  </td>
                  <td className="p-4 text-slate-300">{investment.type}</td>
                  <td className="p-4 text-slate-300">{investment.date}</td>
                  <td className="p-4 text-slate-300">{investment.duration}</td>
                  <td className="p-4">
                    <span className={getRiskColor(investment.risk)}>
                      {investment.risk.charAt(0).toUpperCase() + investment.risk.slice(1)}
                    </span>
                  </td>
                  <td className="p-4">
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(investment.status)}`}>
                      {investment.status.charAt(0).toUpperCase() + investment.status.slice(1)}
                    </span>
                  </td>
                  <td className="p-4">
                    <Button variant="secondary" size="sm">
                      View Details
                    </Button>
                  </td>
                </tr>)}
              {filteredInvestments.length === 0 && <tr>
                  <td colSpan={9} className="p-4 text-center text-slate-400">
                    No investments found matching your criteria
                  </td>
                </tr>}
            </tbody>
          </table>
        </div>
      </div>
    </DashboardLayout>;
};
export default Investments;