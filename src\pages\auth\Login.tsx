import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import { UserIcon, LockIcon } from 'lucide-react';
import { useAuth } from '../../contexts/AuthContext';
import Card from '../../components/ui/Card';
import Input from '../../components/ui/Input';
import Button from '../../components/ui/Button';
const Login: React.FC = () => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [rememberMe, setRememberMe] = useState(false);
  const [errors, setErrors] = useState<{
    email?: string;
    password?: string;
  }>({});
  const {
    login,
    isLoading
  } = useAuth();
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    // Simple validation
    const newErrors: {
      email?: string;
      password?: string;
    } = {};
    if (!email) {
      newErrors.email = 'Email is required';
    } else if (!/\S+@\S+\.\S+/.test(email)) {
      newErrors.email = 'Invalid email format';
    }
    if (!password) {
      newErrors.password = 'Password is required';
    } else if (password.length < 6) {
      newErrors.password = 'Password must be at least 6 characters';
    }
    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors);
      return;
    }
    try {
      await login(email, password);
    } catch (error) {
      console.error('Login error:', error);
      setErrors({
        password: 'Invalid email or password'
      });
    }
  };
  return <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 p-4">
      {/* Animated particles background */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        {[...Array(20)].map((_, i) => <div key={i} className="absolute bg-yellow-500/20 rounded-full" style={{
        width: `${Math.random() * 10 + 5}px`,
        height: `${Math.random() * 10 + 5}px`,
        top: `${Math.random() * 100}%`,
        left: `${Math.random() * 100}%`,
        animation: `float ${Math.random() * 10 + 10}s linear infinite`,
        opacity: Math.random() * 0.5 + 0.2
      }} />)}
      </div>
      <div className="w-full max-w-md">
        {/* Logo */}
        <div className="flex justify-center mb-8">
          <div className="text-center">
            <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-gradient-to-r from-yellow-500 to-yellow-600 mb-4">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-white" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clipRule="evenodd" />
              </svg>
            </div>
            <h1 className="text-2xl font-bold text-white">
              Investment Management
            </h1>
            <p className="text-slate-400 mt-1">Sign in to your account</p>
          </div>
        </div>
        {/* Login form */}
        <Card glassmorphism className="backdrop-blur-lg">
          <form onSubmit={handleSubmit}>
            <div className="space-y-4">
              <Input label="Email" type="email" placeholder="<EMAIL>" value={email} onChange={e => setEmail(e.target.value)} error={errors.email} leftIcon={<UserIcon size={18} />} fullWidth />
              <Input label="Password" type="password" placeholder="••••••••" value={password} onChange={e => setPassword(e.target.value)} error={errors.password} leftIcon={<LockIcon size={18} />} fullWidth />
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <input id="remember-me" name="remember-me" type="checkbox" checked={rememberMe} onChange={e => setRememberMe(e.target.checked)} className="h-4 w-4 text-yellow-500 focus:ring-yellow-500 border-slate-600 rounded bg-slate-700" />
                  <label htmlFor="remember-me" className="ml-2 block text-sm text-slate-300">
                    Remember me
                  </label>
                </div>
                <Link to="/reset-password" className="text-sm text-yellow-500 hover:text-yellow-400">
                  Forgot password?
                </Link>
              </div>
              <Button type="submit" variant="primary" fullWidth isLoading={isLoading}>
                Sign In
              </Button>
            </div>
          </form>
        </Card>

        {/* Registration Link */}
        <div className="text-center mt-6">
          <p className="text-slate-400">
            Don't have an account?{' '}
            <Link to="/register" className="text-yellow-500 hover:text-yellow-400 font-medium">
              Create one here
            </Link>
          </p>
        </div>


      </div>
    </div>;
};
export default Login;