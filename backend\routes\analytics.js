import express from 'express';
import Investment from '../models/Investment.js';
import { SubCompany } from '../models/Company.js';
import User from '../models/User.js';
import Role from '../models/Role.js';
import { authMiddleware, requireRole } from '../middleware/auth.js';

const router = express.Router();

// Get superadmin analytics
router.get('/superadmin', authMiddleware, requireRole(['superadmin']), async (req, res) => {
  try {
    // Get all sub-companies
    const subCompanies = await SubCompany.find().populate('adminUserId', '-password');
    
    // Get all investments
    const investments = await Investment.find().populate(['assetId', 'subCompanyId']);
    
    // Get all users with roles
    const users = await User.find({ isActive: true });
    const roles = await Role.find();
    
    // Calculate metrics
    const totalInvestments = investments.length;
    const totalValue = investments.reduce((sum, inv) => sum + inv.currentValue, 0);
    const totalInitialAmount = investments.reduce((sum, inv) => sum + inv.initialAmount, 0);
    const totalProfit = Math.max(0, totalValue - totalInitialAmount);
    const totalLoss = Math.max(0, totalInitialAmount - totalValue);
    const roi = totalInitialAmount > 0 ? ((totalValue - totalInitialAmount) / totalInitialAmount) * 100 : 0;
    
    const activeInvestments = investments.filter(inv => inv.status === 'Active').length;
    const pendingInvestments = investments.filter(inv => inv.status === 'Pending').length;
    const completedInvestments = investments.filter(inv => inv.status === 'Completed').length;
    
    const totalInvestors = roles.filter(role => role.type === 'investor').length;
    const totalAdmins = roles.filter(role => role.type === 'admin').length;
    
    // Generate monthly performance data (mock for now)
    const monthlyPerformance = [];
    const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'];
    let baseInvestment = 1000000;
    
    for (let i = 0; i < months.length; i++) {
      const monthlyInvestment = baseInvestment + (i * 200000);
      const monthlyReturn = monthlyInvestment * (1 + (roi / 100) / 12);
      const monthlyROI = ((monthlyReturn - monthlyInvestment) / monthlyInvestment) * 100;
      const monthlyProfit = Math.max(0, monthlyReturn - monthlyInvestment);
      
      monthlyPerformance.push({
        month: months[i],
        year: 2024,
        totalInvestment: monthlyInvestment,
        totalReturn: monthlyReturn,
        roi: monthlyROI,
        profit: monthlyProfit,
        loss: 0
      });
    }
    
    // Transform sub-companies data
    const topPerformingCompanies = subCompanies.map(company => ({
      id: company._id,
      owner_company_id: company.ownerCompanyId,
      name: company.name,
      industry: company.industry,
      description: company.description,
      established_date: company.establishedDate,
      status: company.status,
      created_at: company.createdAt,
      updated_at: company.updatedAt,
      totalInvestments: investments.filter(inv => inv.subCompanyId.toString() === company._id.toString()).length,
      totalInvestors: 0, // TODO: Calculate from investor investments
      totalValue: investments
        .filter(inv => inv.subCompanyId.toString() === company._id.toString())
        .reduce((sum, inv) => sum + inv.currentValue, 0),
      profitLoss: {
        profit: 0,
        loss: 0,
        roi: 0
      }
    }));

    const analytics = {
      totalInvestments,
      totalInvestors,
      totalValue,
      totalProfit,
      totalLoss,
      roi,
      monthlyGrowth: 8.5, // Mock value
      activeInvestments,
      pendingInvestments,
      completedInvestments,
      totalSubCompanies: subCompanies.length,
      totalAdmins,
      topPerformingCompanies,
      monthlyPerformance,
      recentActivities: [] // TODO: Implement activity logs
    };

    res.json({
      success: true,
      data: analytics
    });

  } catch (error) {
    console.error('Get superadmin analytics error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

// Get admin analytics
router.get('/admin', authMiddleware, requireRole(['admin']), async (req, res) => {
  try {
    const { subCompanyId } = req.query;
    
    // Get sub-company
    const subCompany = await SubCompany.findById(subCompanyId);
    if (!subCompany) {
      return res.status(404).json({
        success: false,
        message: 'Sub-company not found'
      });
    }
    
    // Get investments for this sub-company
    const investments = await Investment.find({ subCompanyId }).populate(['assetId', 'subCompanyId']);
    
    // Calculate metrics
    const totalInvestments = investments.length;
    const totalValue = investments.reduce((sum, inv) => sum + inv.currentValue, 0);
    const totalInitialAmount = investments.reduce((sum, inv) => sum + inv.initialAmount, 0);
    const totalProfit = Math.max(0, totalValue - totalInitialAmount);
    const totalLoss = Math.max(0, totalInitialAmount - totalValue);
    const roi = totalInitialAmount > 0 ? ((totalValue - totalInitialAmount) / totalInitialAmount) * 100 : 0;
    
    const activeInvestments = investments.filter(inv => inv.status === 'Active').length;
    const pendingInvestments = investments.filter(inv => inv.status === 'Pending').length;
    const completedInvestments = investments.filter(inv => inv.status === 'Completed').length;
    
    // Generate monthly performance data
    const monthlyPerformance = [];
    const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'];
    let baseInvestment = 400000;
    
    for (let i = 0; i < months.length; i++) {
      const monthlyInvestment = baseInvestment + (i * 100000);
      const monthlyReturn = monthlyInvestment * (1 + (roi / 100) / 12);
      const monthlyROI = ((monthlyReturn - monthlyInvestment) / monthlyInvestment) * 100;
      const monthlyProfit = Math.max(0, monthlyReturn - monthlyInvestment);
      
      monthlyPerformance.push({
        month: months[i],
        year: 2024,
        totalInvestment: monthlyInvestment,
        totalReturn: monthlyReturn,
        roi: monthlyROI,
        profit: monthlyProfit,
        loss: 0
      });
    }
    
    // Transform investments data
    const topInvestments = investments.map(investment => ({
      id: investment._id,
      sub_company_id: investment.subCompanyId._id,
      asset_id: investment.assetId._id,
      name: investment.name,
      description: investment.description,
      initial_amount: investment.initialAmount,
      current_value: investment.currentValue,
      min_investment: investment.minInvestment,
      max_investment: investment.maxInvestment,
      expected_roi: investment.expectedROI,
      actual_roi: investment.actualROI,
      start_date: investment.startDate,
      end_date: investment.endDate,
      status: investment.status,
      risk_level: investment.riskLevel,
      created_at: investment.createdAt,
      updated_at: investment.updatedAt,
      asset: {
        id: investment.assetId._id,
        name: investment.assetId.name,
        type: investment.assetId.type,
        current_price: investment.assetId.currentPrice,
        logo: investment.assetId.logo
      },
      currentROI: investment.currentROI
    }));

    const analytics = {
      totalInvestments,
      totalInvestors: 0, // TODO: Calculate from investor investments
      totalValue,
      totalProfit,
      totalLoss,
      roi,
      monthlyGrowth: 12.5, // Mock value
      activeInvestments,
      pendingInvestments,
      completedInvestments,
      subCompany: {
        id: subCompany._id,
        name: subCompany.name,
        industry: subCompany.industry,
        status: subCompany.status
      },
      topInvestments,
      recentInvestors: [], // TODO: Implement
      monthlyPerformance
    };

    res.json({
      success: true,
      data: analytics
    });

  } catch (error) {
    console.error('Get admin analytics error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

// Get investor analytics
router.get('/investor', authMiddleware, requireRole(['investor']), async (req, res) => {
  try {
    const { userId } = req.query;
    const targetUserId = userId || req.user._id;
    
    // Get available investments
    const availableInvestments = await Investment.find({ 
      status: 'Active',
      isPublic: true 
    }).populate(['assetId', 'subCompanyId']);
    
    // Mock investor portfolio data (in real app, get from InvestorInvestment model)
    const portfolioValue = 75000;
    const totalInvested = 65000;
    const totalProfit = Math.max(0, portfolioValue - totalInvested);
    const totalLoss = Math.max(0, totalInvested - portfolioValue);
    const roi = totalInvested > 0 ? ((portfolioValue - totalInvested) / totalInvested) * 100 : 0;
    
    const analytics = {
      totalInvestments: 3,
      totalInvestors: 1,
      totalValue: portfolioValue,
      totalProfit,
      totalLoss,
      roi,
      monthlyGrowth: 8.5,
      activeInvestments: 2,
      pendingInvestments: 1,
      completedInvestments: 0,
      portfolio: [], // TODO: Get actual portfolio
      availableInvestments: availableInvestments.map(investment => ({
        id: investment._id,
        sub_company_id: investment.subCompanyId._id,
        asset_id: investment.assetId._id,
        name: investment.name,
        description: investment.description,
        initial_amount: investment.initialAmount,
        current_value: investment.currentValue,
        min_investment: investment.minInvestment,
        max_investment: investment.maxInvestment,
        expected_roi: investment.expectedROI,
        start_date: investment.startDate,
        status: investment.status,
        risk_level: investment.riskLevel,
        created_at: investment.createdAt,
        updated_at: investment.updatedAt,
        asset: {
          id: investment.assetId._id,
          name: investment.assetId.name,
          type: investment.assetId.type,
          current_price: investment.assetId.currentPrice,
          logo: investment.assetId.logo
        },
        currentROI: investment.currentROI
      })),
      recentTransactions: [],
      portfolioDistribution: [
        { assetType: 'Business', value: 30000, percentage: 40, count: 1 },
        { assetType: 'Stock', value: 25000, percentage: 33.3, count: 1 },
        { assetType: 'Crypto', value: 20000, percentage: 26.7, count: 1 }
      ]
    };

    res.json({
      success: true,
      data: analytics
    });

  } catch (error) {
    console.error('Get investor analytics error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

export default router;
