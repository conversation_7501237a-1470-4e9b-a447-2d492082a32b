import express from 'express';
import { User, Role } from '../models/index.js';

const router = express.Router();

// Get all users (admin/superadmin only)
router.get('/', authMiddleware, async (req, res) => {
  try {
    const users = await User.find({ isActive: true })
      .select('-password')
      .sort({ createdAt: -1 });

    // Get roles for all users
    const userIds = users.map(user => user._id);
    const roles = await Role.find({ userId: { $in: userIds } });

    // Combine users with their roles
    const usersWithRoles = users.map(user => {
      const userRole = roles.find(role => role.userId.toString() === user._id.toString());
      return {
        id: user._id,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        phone: user.phone,
        address: user.address,
        profilePicture: user.profilePicture,
        isActive: user.isActive,
        lastLogin: user.lastLogin,
        created_at: user.createdAt,
        updated_at: user.updatedAt,
        role: userRole ? {
          id: userRole._id,
          user_id: user._id,
          type: userRole.type,
          permissions: userRole.permissions,
          status: userRole.status,
          created_at: userRole.createdAt
        } : null
      };
    });

    res.json({
      success: true,
      data: usersWithRoles
    });

  } catch (error) {
    console.error('Get users error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

// Get user by ID
router.get('/:id', authMiddleware, async (req, res) => {
  try {
    const user = await User.findById(req.params.id).select('-password');
    
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    const role = await Role.findOne({ userId: user._id });

    const userResponse = {
      id: user._id,
      email: user.email,
      firstName: user.firstName,
      lastName: user.lastName,
      phone: user.phone,
      address: user.address,
      dateOfBirth: user.dateOfBirth,
      profilePicture: user.profilePicture,
      isActive: user.isActive,
      lastLogin: user.lastLogin,
      created_at: user.createdAt,
      updated_at: user.updatedAt,
      role: role ? {
        id: role._id,
        user_id: user._id,
        type: role.type,
        permissions: role.permissions,
        status: role.status,
        created_at: role.createdAt
      } : null
    };

    res.json({
      success: true,
      data: userResponse
    });

  } catch (error) {
    console.error('Get user error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

// Update user
router.put('/:id', authMiddleware, async (req, res) => {
  try {
    const { firstName, lastName, phone, address, dateOfBirth } = req.body;

    const user = await User.findById(req.params.id);
    
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // Update user fields
    if (firstName) user.firstName = firstName;
    if (lastName) user.lastName = lastName;
    if (phone) user.phone = phone;
    if (address) user.address = address;
    if (dateOfBirth) user.dateOfBirth = dateOfBirth;

    await user.save();

    const role = await Role.findOne({ userId: user._id });

    const userResponse = {
      id: user._id,
      email: user.email,
      firstName: user.firstName,
      lastName: user.lastName,
      phone: user.phone,
      address: user.address,
      dateOfBirth: user.dateOfBirth,
      profilePicture: user.profilePicture,
      isActive: user.isActive,
      lastLogin: user.lastLogin,
      created_at: user.createdAt,
      updated_at: user.updatedAt,
      role: role ? {
        id: role._id,
        user_id: user._id,
        type: role.type,
        permissions: role.permissions,
        status: role.status,
        created_at: role.createdAt
      } : null
    };

    res.json({
      success: true,
      message: 'User updated successfully',
      data: userResponse
    });

  } catch (error) {
    console.error('Update user error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

// Update user status
router.patch('/:id/status', authMiddleware, async (req, res) => {
  try {
    const { status } = req.body;

    if (!['active', 'inactive', 'pending'].includes(status)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid status value'
      });
    }

    const role = await Role.findOne({ userId: req.params.id });
    
    if (!role) {
      return res.status(404).json({
        success: false,
        message: 'User role not found'
      });
    }

    role.status = status;
    await role.save();

    res.json({
      success: true,
      message: 'User status updated successfully'
    });

  } catch (error) {
    console.error('Update user status error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

// Delete user (soft delete)
router.delete('/:id', authMiddleware, async (req, res) => {
  try {
    const user = await User.findById(req.params.id);
    
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    user.isActive = false;
    await user.save();

    res.json({
      success: true,
      message: 'User deleted successfully'
    });

  } catch (error) {
    console.error('Delete user error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

export default router;
