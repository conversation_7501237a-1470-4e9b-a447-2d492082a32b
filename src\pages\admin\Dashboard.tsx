import React, { useEffect } from 'react';
import { TrendingUpIcon, DollarSignIcon, PercentIcon, UsersIcon, PieChartIcon } from 'lucide-react';
import DashboardLayout from '../../layouts/DashboardLayout';
import StatCard from '../../components/dashboard/StatCard';
import PerformanceChart from '../../components/dashboard/PerformanceChart';
import Button from '../../components/ui/Button';
import { useAuth } from '../../contexts/AuthContext';
import { useInvestment } from '../../contexts/InvestmentContext';
import { useNavigate } from 'react-router-dom';
const AdminDashboard: React.FC = () => {
  const {
    user
  } = useAuth();
  const {
    investments,
    fetchInvestments,
    loading
  } = useInvestment();
  const navigate = useNavigate();
  useEffect(() => {
    if (user?.companyId) {
      fetchInvestments(user.companyId);
    }
  }, [user]);
  // Calculate stats
  const totalInvestments = investments.length;
  const activeInvestments = investments.filter(inv => inv.status === 'Performing').length;
  const totalValue = investments.reduce((sum, inv) => sum + inv.amount, 0);
  const avgRoi = investments.length > 0 ? investments.reduce((sum, inv) => sum + inv.roi, 0) / investments.length : 0;
  // Performance chart data
  const performanceData = [{
    name: 'Jan',
    value: 120000,
    benchmark: 110000
  }, {
    name: 'Feb',
    value: 135000,
    benchmark: 115000
  }, {
    name: 'Mar',
    value: 128000,
    benchmark: 118000
  }, {
    name: 'Apr',
    value: 145000,
    benchmark: 125000
  }, {
    name: 'May',
    value: 160000,
    benchmark: 135000
  }, {
    name: 'Jun',
    value: 178000,
    benchmark: 145000
  }];
  return <DashboardLayout title="Admin Dashboard" subtitle="Manage your company's investments and investors">
      {/* Top stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
        <StatCard title="Total Investments" value={totalInvestments} icon={<PieChartIcon size={24} className="text-blue-500" />} />
        <StatCard title="Active Investments" value={activeInvestments} icon={<TrendingUpIcon size={24} className="text-teal-500" />} trend={4.5} trendLabel="vs last month" />
        <StatCard title="Total Value" value={`$${totalValue.toLocaleString()}`} icon={<DollarSignIcon size={24} className="text-green-500" />} trend={8.3} trendLabel="vs last month" />
        <StatCard title="Average ROI" value={`${avgRoi.toFixed(1)}%`} icon={<PercentIcon size={24} className="text-yellow-500" />} trend={1.8} trendLabel="vs last month" />
      </div>
      {/* Middle section */}
      <div className="mb-6">
        <PerformanceChart title="Company Performance (6 Months)" data={performanceData} className="bg-gradient-to-br from-slate-800 to-slate-800/80" />
      </div>
      {/* Bottom section */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2 bg-slate-800 rounded-lg p-6">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-medium text-white">
              Recent Investments
            </h3>
            <Button variant="secondary" size="sm" onClick={() => navigate('/admin/investments')}>
              View All
            </Button>
          </div>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="text-left text-sm text-slate-400 border-b border-slate-700">
                  <th className="pb-3 font-medium">Name</th>
                  <th className="pb-3 font-medium">Type</th>
                  <th className="pb-3 font-medium">Amount</th>
                  <th className="pb-3 font-medium">ROI</th>
                  <th className="pb-3 font-medium">Status</th>
                </tr>
              </thead>
              <tbody>
                {loading ? <tr>
                    <td colSpan={5} className="py-4 text-center">
                      <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-yellow-400 mx-auto"></div>
                    </td>
                  </tr> : investments.length > 0 ? investments.slice(0, 5).map((inv, index) => <tr key={inv.id} className="border-b border-slate-700">
                      <td className="py-3 text-white">{inv.name}</td>
                      <td className="py-3 text-slate-300">{inv.type}</td>
                      <td className="py-3 text-white">
                        ${inv.amount.toLocaleString()}
                      </td>
                      <td className="py-3 text-yellow-500">{inv.roi}%</td>
                      <td className="py-3">
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${inv.status === 'Performing' ? 'bg-green-500/20 text-green-500' : inv.status === 'At Risk' ? 'bg-yellow-500/20 text-yellow-500' : 'bg-blue-500/20 text-blue-500'}`}>
                          {inv.status}
                        </span>
                      </td>
                    </tr>) : <tr>
                    <td colSpan={5} className="py-4 text-center text-slate-400">
                      No investments found
                    </td>
                  </tr>}
              </tbody>
            </table>
          </div>
        </div>
        <div className="bg-slate-800 rounded-lg p-6">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-medium text-white">
              Investor Overview
            </h3>
            <Button variant="secondary" size="sm" onClick={() => navigate('/admin/investors')}>
              View All
            </Button>
          </div>
          <div className="space-y-4">
            <div className="bg-slate-700 p-4 rounded-lg flex items-center justify-between">
              <div className="flex items-center">
                <div className="w-10 h-10 rounded-full bg-blue-500/20 flex items-center justify-center mr-3">
                  <UsersIcon size={20} className="text-blue-500" />
                </div>
                <div>
                  <h4 className="text-white font-medium">Total Investors</h4>
                  <p className="text-slate-400 text-sm">Active accounts</p>
                </div>
              </div>
              <div className="text-2xl font-bold text-white">24</div>
            </div>
            <div className="bg-slate-700 p-4 rounded-lg flex items-center justify-between">
              <div className="flex items-center">
                <div className="w-10 h-10 rounded-full bg-green-500/20 flex items-center justify-center mr-3">
                  <DollarSignIcon size={20} className="text-green-500" />
                </div>
                <div>
                  <h4 className="text-white font-medium">Avg. Investment</h4>
                  <p className="text-slate-400 text-sm">Per investor</p>
                </div>
              </div>
              <div className="text-2xl font-bold text-white">$42.5K</div>
            </div>
            <div className="bg-slate-700 p-4 rounded-lg flex items-center justify-between">
              <div className="flex items-center">
                <div className="w-10 h-10 rounded-full bg-yellow-500/20 flex items-center justify-center mr-3">
                  <TrendingUpIcon size={20} className="text-yellow-500" />
                </div>
                <div>
                  <h4 className="text-white font-medium">Investor Growth</h4>
                  <p className="text-slate-400 text-sm">Last 30 days</p>
                </div>
              </div>
              <div className="text-2xl font-bold text-green-500">+12%</div>
            </div>
          </div>
        </div>
      </div>
    </DashboardLayout>;
};
export default AdminDashboard;