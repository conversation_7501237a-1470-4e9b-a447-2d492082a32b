import React, { useState, createContext, useContext } from 'react';
interface Investment {
  id: string;
  name: string;
  type: 'Stocks' | 'Real Estate' | 'Crypto' | 'Business';
  amount: number;
  roi: number;
  startDate: string;
  term: string;
  status: 'Performing' | 'At Risk' | 'Closed';
  companyId: string;
}
interface InvestmentContextType {
  investments: Investment[];
  loading: boolean;
  fetchInvestments: (companyId?: string) => Promise<void>;
  getInvestmentById: (id: string) => Investment | undefined;
}
const InvestmentContext = createContext<InvestmentContextType>({
  investments: [],
  loading: false,
  fetchInvestments: async () => {},
  getInvestmentById: () => undefined
});
export const useInvestment = () => useContext(InvestmentContext);
export const InvestmentProvider: React.FC<{
  children: React.ReactNode;
}> = ({
  children
}) => {
  const [investments, setInvestments] = useState<Investment[]>([]);
  const [loading, setLoading] = useState(false);
  const fetchInvestments = async (companyId?: string) => {
    setLoading(true);
    try {
      // This would be an API call in a real application
      // Simulating API response for demo
      const mockInvestments: Investment[] = [{
        id: 'inv1',
        name: 'TikTok Advertising Fund',
        type: 'Business',
        amount: 500000,
        roi: 22.5,
        startDate: '2023-02-10',
        term: '12 months',
        status: 'Performing',
        companyId: 'comp1'
      }, {
        id: 'inv2',
        name: 'Facebook Ad Campaign',
        type: 'Business',
        amount: 350000,
        roi: 15.2,
        startDate: '2023-04-05',
        term: '6 months',
        status: 'At Risk',
        companyId: 'comp1'
      }, {
        id: 'inv3',
        name: 'Walmart Logistics Partnership',
        type: 'Business',
        amount: 1200000,
        roi: 18.7,
        startDate: '2023-01-20',
        term: '24 months',
        status: 'Performing',
        companyId: 'comp2'
      }];
      // Filter by companyId if provided
      const filteredInvestments = companyId ? mockInvestments.filter(inv => inv.companyId === companyId) : mockInvestments;
      setInvestments(filteredInvestments);
    } catch (error) {
      console.error('Failed to fetch investments:', error);
    } finally {
      setLoading(false);
    }
  };
  const getInvestmentById = (id: string): Investment | undefined => {
    return investments.find(investment => investment.id === id);
  };
  return <InvestmentContext.Provider value={{
    investments,
    loading,
    fetchInvestments,
    getInvestmentById
  }}>
      {children}
    </InvestmentContext.Provider>;
};