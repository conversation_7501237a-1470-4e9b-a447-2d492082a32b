import React, { useEffect } from 'react';
import { BuildingIcon, TrendingUpIcon, DollarSignIcon, PercentIcon } from 'lucide-react';
import DashboardLayout from '../../layouts/DashboardLayout';
import StatCard from '../../components/dashboard/StatCard';
import CompanyTable from '../../components/dashboard/CompanyTable';
import PerformanceChart from '../../components/dashboard/PerformanceChart';
import InvestmentTreemap from '../../components/dashboard/InvestmentTreemap';
import Button from '../../components/ui/Button';
import { useNavigate } from 'react-router-dom';
import { useCompany } from '../../contexts/CompanyContext';
import { useInvestment } from '../../contexts/InvestmentContext';
import { useMarketData } from '../../contexts/MarketDataContext';
const SuperadminDashboard: React.FC = () => {
  const navigate = useNavigate();
  const {
    companies,
    fetchCompanies,
    loading: loadingCompanies
  } = useCompany();
  const {
    investments,
    fetchInvestments,
    loading: loadingInvestments
  } = useInvestment();
  const {
    marketData,
    fetchMarketData,
    loading: loadingMarketData
  } = useMarketData();
  useEffect(() => {
    fetchCompanies();
    fetchInvestments();
    fetchMarketData();
  }, []);
  // Calculate total stats
  const totalCompanies = companies.length;
  const activeInvestments = investments.filter(inv => inv.status === 'Performing').length;
  const totalProfit = companies.reduce((sum, company) => sum + company.performance.profit, 0);
  const totalLoss = companies.reduce((sum, company) => sum + company.performance.loss, 0);
  const netProfit = totalProfit - totalLoss;
  const avgRoi = companies.length > 0 ? companies.reduce((sum, company) => sum + company.performance.roi, 0) / companies.length : 0;
  // Performance chart data
  const performanceData = [{
    name: 'Jan',
    value: 150000,
    benchmark: 145000
  }, {
    name: 'Feb',
    value: 210000,
    benchmark: 190000
  }, {
    name: 'Mar',
    value: 180000,
    benchmark: 170000
  }, {
    name: 'Apr',
    value: 250000,
    benchmark: 210000
  }, {
    name: 'May',
    value: 290000,
    benchmark: 240000
  }, {
    name: 'Jun',
    value: 320000,
    benchmark: 270000
  }];
  // Investment allocation data
  const investmentData = investments.slice(0, 5).map((inv, index) => {
    const colors = ['#E6B325', '#2DD4BF', '#E11D48', '#8B5CF6', '#3B82F6'];
    return {
      name: inv.name.split(' ')[0],
      value: inv.amount,
      roi: inv.roi,
      company: companies.find(c => c.id === inv.companyId)?.name || 'Unknown',
      fill: colors[index % colors.length]
    };
  });
  return <DashboardLayout title="Superadmin Dashboard" subtitle="Overview of all sub-companies and investments">
      {/* Top stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
        <StatCard title="Total Sub-Companies" value={totalCompanies} icon={<BuildingIcon size={24} className="text-yellow-500" />} />
        <StatCard title="Active Investments" value={activeInvestments} icon={<TrendingUpIcon size={24} className="text-teal-500" />} trend={8.2} trendLabel="vs last month" />
        <StatCard title="Net Profit/Loss" value={`$${netProfit.toLocaleString()}`} icon={<DollarSignIcon size={24} className="text-green-500" />} trend={12.5} trendLabel="vs last month" />
        <StatCard title="Average ROI" value={`${avgRoi.toFixed(1)}%`} icon={<PercentIcon size={24} className="text-blue-500" />} trend={2.3} trendLabel="vs last month" />
      </div>
      {/* Middle section - Performance Chart */}
      <div className="mb-6">
        <PerformanceChart title="Performance Trend (6 Months)" data={performanceData} className="bg-gradient-to-br from-slate-800 to-slate-800/80" />
      </div>
      {/* Bottom section */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2">
          <CompanyTable companies={companies} isLoading={loadingCompanies} />
        </div>
        <div>
          <div className="mb-4">
            <Button variant="primary" fullWidth onClick={() => navigate('/superadmin/company/new')}>
              Create Sub-Company
            </Button>
          </div>
          <InvestmentTreemap title="Investment Allocation" data={investmentData} />
        </div>
      </div>
    </DashboardLayout>;
};
export default SuperadminDashboard;