import nodemailer from 'nodemailer';
import dotenv from 'dotenv';

dotenv.config();

// Email configuration
const emailConfig = {
  host: process.env.EMAIL_HOST || 'smtp.gmail.com',
  port: process.env.EMAIL_PORT || 587,
  secure: false, // true for 465, false for other ports
  auth: {
    user: process.env.EMAIL_USER,
    pass: process.env.EMAIL_PASS
  }
};

// Create transporter
let transporter;
try {
  transporter = nodemailer.createTransport(emailConfig);
} catch (error) {
  console.error('Email transporter configuration error:', error);
}

// Verify email configuration
const verifyEmailConfig = async () => {
  if (!transporter) return false;
  
  try {
    await transporter.verify();
    console.log('✅ Email service is ready');
    return true;
  } catch (error) {
    console.error('❌ Email service configuration error:', error);
    return false;
  }
};

// Initialize email service
verifyEmailConfig();

// Send verification email
export const sendVerificationEmail = async (email, firstName, token) => {
  if (!transporter) {
    console.log('📧 Email service not configured - verification email would be sent to:', email);
    console.log('🔗 Verification token:', token);
    return; // In development, just log the token
  }

  const verificationUrl = `${process.env.FRONTEND_URL || 'http://localhost:5174'}/verify-email?token=${token}`;

  const mailOptions = {
    from: `"Investment Management System" <${process.env.EMAIL_USER}>`,
    to: email,
    subject: 'Verify Your Email Address',
    html: `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Email Verification</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: #1e293b; color: white; padding: 20px; text-align: center; }
          .content { background: #f8fafc; padding: 30px; }
          .button { display: inline-block; background: #eab308; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; margin: 20px 0; }
          .footer { background: #64748b; color: white; padding: 20px; text-align: center; font-size: 14px; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>🚀 Investment Management System</h1>
          </div>
          <div class="content">
            <h2>Welcome, ${firstName}!</h2>
            <p>Thank you for registering with our Investment Management System. To complete your registration and secure your account, please verify your email address.</p>
            
            <p>Click the button below to verify your email:</p>
            <a href="${verificationUrl}" class="button">Verify Email Address</a>
            
            <p>Or copy and paste this link into your browser:</p>
            <p style="word-break: break-all; background: #e2e8f0; padding: 10px; border-radius: 5px;">${verificationUrl}</p>
            
            <p><strong>Important:</strong> This verification link will expire in 24 hours for security reasons.</p>
            
            <p>If you didn't create an account with us, please ignore this email.</p>
            
            <p>Best regards,<br>The Investment Management Team</p>
          </div>
          <div class="footer">
            <p>&copy; 2024 Investment Management System. All rights reserved.</p>
            <p>This is an automated email. Please do not reply to this message.</p>
          </div>
        </div>
      </body>
      </html>
    `
  };

  try {
    const info = await transporter.sendMail(mailOptions);
    console.log('✅ Verification email sent:', info.messageId);
    return info;
  } catch (error) {
    console.error('❌ Failed to send verification email:', error);
    throw error;
  }
};

// Send password reset email
export const sendPasswordResetEmail = async (email, firstName, token) => {
  if (!transporter) {
    console.log('📧 Email service not configured - password reset email would be sent to:', email);
    console.log('🔗 Reset token:', token);
    return; // In development, just log the token
  }

  const resetUrl = `${process.env.FRONTEND_URL || 'http://localhost:5174'}/reset-password?token=${token}`;

  const mailOptions = {
    from: `"Investment Management System" <${process.env.EMAIL_USER}>`,
    to: email,
    subject: 'Reset Your Password',
    html: `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Password Reset</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: #1e293b; color: white; padding: 20px; text-align: center; }
          .content { background: #f8fafc; padding: 30px; }
          .button { display: inline-block; background: #ef4444; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; margin: 20px 0; }
          .footer { background: #64748b; color: white; padding: 20px; text-align: center; font-size: 14px; }
          .warning { background: #fef3c7; border: 1px solid #f59e0b; padding: 15px; border-radius: 5px; margin: 20px 0; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>🔒 Password Reset Request</h1>
          </div>
          <div class="content">
            <h2>Hello, ${firstName}</h2>
            <p>We received a request to reset the password for your Investment Management System account.</p>
            
            <div class="warning">
              <strong>⚠️ Security Notice:</strong> If you didn't request this password reset, please ignore this email and consider changing your password as a precaution.
            </div>
            
            <p>To reset your password, click the button below:</p>
            <a href="${resetUrl}" class="button">Reset Password</a>
            
            <p>Or copy and paste this link into your browser:</p>
            <p style="word-break: break-all; background: #e2e8f0; padding: 10px; border-radius: 5px;">${resetUrl}</p>
            
            <p><strong>Important:</strong> This password reset link will expire in 1 hour for security reasons.</p>
            
            <p>After clicking the link, you'll be able to create a new password for your account.</p>
            
            <p>Best regards,<br>The Investment Management Team</p>
          </div>
          <div class="footer">
            <p>&copy; 2024 Investment Management System. All rights reserved.</p>
            <p>This is an automated email. Please do not reply to this message.</p>
          </div>
        </div>
      </body>
      </html>
    `
  };

  try {
    const info = await transporter.sendMail(mailOptions);
    console.log('✅ Password reset email sent:', info.messageId);
    return info;
  } catch (error) {
    console.error('❌ Failed to send password reset email:', error);
    throw error;
  }
};

// Send welcome email after email verification
export const sendWelcomeEmail = async (email, firstName) => {
  if (!transporter) {
    console.log('📧 Email service not configured - welcome email would be sent to:', email);
    return;
  }

  const mailOptions = {
    from: `"Investment Management System" <${process.env.EMAIL_USER}>`,
    to: email,
    subject: 'Welcome to Investment Management System!',
    html: `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Welcome</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: #1e293b; color: white; padding: 20px; text-align: center; }
          .content { background: #f8fafc; padding: 30px; }
          .button { display: inline-block; background: #10b981; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; margin: 20px 0; }
          .footer { background: #64748b; color: white; padding: 20px; text-align: center; font-size: 14px; }
          .feature { background: white; padding: 15px; margin: 10px 0; border-radius: 5px; border-left: 4px solid #eab308; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>🎉 Welcome to Investment Management!</h1>
          </div>
          <div class="content">
            <h2>Hello, ${firstName}!</h2>
            <p>Congratulations! Your email has been verified and your account is now active. You can start exploring our comprehensive investment management platform.</p>
            
            <h3>🚀 What you can do now:</h3>
            
            <div class="feature">
              <strong>📊 View Analytics:</strong> Access detailed performance metrics and insights
            </div>
            
            <div class="feature">
              <strong>💼 Manage Investments:</strong> Track and manage your investment portfolio
            </div>
            
            <div class="feature">
              <strong>📈 Generate Reports:</strong> Create comprehensive investment reports
            </div>
            
            <div class="feature">
              <strong>🔒 Secure Account:</strong> Your account is protected with industry-standard security
            </div>
            
            <p>Ready to get started?</p>
            <a href="${process.env.FRONTEND_URL || 'http://localhost:5174'}/login" class="button">Login to Your Account</a>
            
            <p>If you have any questions or need assistance, please don't hesitate to contact our support team.</p>
            
            <p>Best regards,<br>The Investment Management Team</p>
          </div>
          <div class="footer">
            <p>&copy; 2024 Investment Management System. All rights reserved.</p>
            <p>Need help? Contact <NAME_EMAIL></p>
          </div>
        </div>
      </body>
      </html>
    `
  };

  try {
    const info = await transporter.sendMail(mailOptions);
    console.log('✅ Welcome email sent:', info.messageId);
    return info;
  } catch (error) {
    console.error('❌ Failed to send welcome email:', error);
    throw error;
  }
};
