import express from 'express';
import Asset from '../models/Asset.js';
import { authMiddleware } from '../middleware/auth.js';

const router = express.Router();

// Get all assets
router.get('/', authMiddleware, async (req, res) => {
  try {
    const { type, isActive } = req.query;
    
    // Build filter
    const filter = {};
    if (type) filter.type = type;
    if (isActive !== undefined) filter.isActive = isActive === 'true';

    const assets = await Asset.find(filter).sort({ name: 1 });

    // Transform data to match frontend expectations
    const transformedAssets = assets.map(asset => ({
      id: asset._id,
      name: asset.name,
      type: asset.type,
      market_symbol: asset.symbol,
      description: asset.description,
      current_price: asset.currentPrice,
      price_change_24h: asset.priceChange24h,
      price_change_percentage: asset.priceChangePercentage,
      market_cap: asset.marketCap,
      volume_24h: asset.volume24h,
      logo: asset.logo,
      website: asset.website,
      is_active: asset.isActive,
      exchange: asset.exchange,
      sector: asset.sector,
      country: asset.country,
      currency: asset.currency,
      last_updated: asset.lastUpdated,
      created_at: asset.createdAt,
      updated_at: asset.updatedAt
    }));

    res.json({
      success: true,
      data: transformedAssets
    });

  } catch (error) {
    console.error('Get assets error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

// Get asset by ID
router.get('/:id', authMiddleware, async (req, res) => {
  try {
    const asset = await Asset.findById(req.params.id);

    if (!asset) {
      return res.status(404).json({
        success: false,
        message: 'Asset not found'
      });
    }

    const transformedAsset = {
      id: asset._id,
      name: asset.name,
      type: asset.type,
      market_symbol: asset.symbol,
      description: asset.description,
      current_price: asset.currentPrice,
      price_change_24h: asset.priceChange24h,
      price_change_percentage: asset.priceChangePercentage,
      market_cap: asset.marketCap,
      volume_24h: asset.volume24h,
      logo: asset.logo,
      website: asset.website,
      is_active: asset.isActive,
      exchange: asset.exchange,
      sector: asset.sector,
      country: asset.country,
      currency: asset.currency,
      last_updated: asset.lastUpdated,
      price_history: asset.priceHistory,
      created_at: asset.createdAt,
      updated_at: asset.updatedAt
    };

    res.json({
      success: true,
      data: transformedAsset
    });

  } catch (error) {
    console.error('Get asset error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

// Create asset
router.post('/', authMiddleware, async (req, res) => {
  try {
    const {
      name,
      type,
      symbol,
      description,
      currentPrice,
      marketCap,
      volume24h,
      logo,
      website,
      exchange,
      sector,
      country,
      currency
    } = req.body;

    const asset = new Asset({
      name,
      type,
      symbol,
      description,
      currentPrice,
      marketCap,
      volume24h,
      logo,
      website,
      exchange,
      sector,
      country,
      currency: currency || 'USD'
    });

    await asset.save();

    const transformedAsset = {
      id: asset._id,
      name: asset.name,
      type: asset.type,
      market_symbol: asset.symbol,
      description: asset.description,
      current_price: asset.currentPrice,
      price_change_24h: asset.priceChange24h,
      price_change_percentage: asset.priceChangePercentage,
      market_cap: asset.marketCap,
      volume_24h: asset.volume24h,
      logo: asset.logo,
      website: asset.website,
      is_active: asset.isActive,
      exchange: asset.exchange,
      sector: asset.sector,
      country: asset.country,
      currency: asset.currency,
      last_updated: asset.lastUpdated,
      created_at: asset.createdAt,
      updated_at: asset.updatedAt
    };

    res.status(201).json({
      success: true,
      message: 'Asset created successfully',
      data: transformedAsset
    });

  } catch (error) {
    console.error('Create asset error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

// Update asset
router.put('/:id', authMiddleware, async (req, res) => {
  try {
    const {
      name,
      type,
      symbol,
      description,
      currentPrice,
      marketCap,
      volume24h,
      logo,
      website,
      isActive,
      exchange,
      sector,
      country,
      currency
    } = req.body;

    const asset = await Asset.findById(req.params.id);
    
    if (!asset) {
      return res.status(404).json({
        success: false,
        message: 'Asset not found'
      });
    }

    // Update fields
    if (name) asset.name = name;
    if (type) asset.type = type;
    if (symbol) asset.symbol = symbol;
    if (description) asset.description = description;
    if (currentPrice !== undefined) {
      // Add to price history if price changed
      if (asset.currentPrice !== currentPrice) {
        await asset.addPriceHistory(currentPrice, volume24h);
      }
    }
    if (marketCap !== undefined) asset.marketCap = marketCap;
    if (volume24h !== undefined) asset.volume24h = volume24h;
    if (logo) asset.logo = logo;
    if (website) asset.website = website;
    if (isActive !== undefined) asset.isActive = isActive;
    if (exchange) asset.exchange = exchange;
    if (sector) asset.sector = sector;
    if (country) asset.country = country;
    if (currency) asset.currency = currency;

    await asset.save();

    const transformedAsset = {
      id: asset._id,
      name: asset.name,
      type: asset.type,
      market_symbol: asset.symbol,
      description: asset.description,
      current_price: asset.currentPrice,
      price_change_24h: asset.priceChange24h,
      price_change_percentage: asset.priceChangePercentage,
      market_cap: asset.marketCap,
      volume_24h: asset.volume24h,
      logo: asset.logo,
      website: asset.website,
      is_active: asset.isActive,
      exchange: asset.exchange,
      sector: asset.sector,
      country: asset.country,
      currency: asset.currency,
      last_updated: asset.lastUpdated,
      created_at: asset.createdAt,
      updated_at: asset.updatedAt
    };

    res.json({
      success: true,
      message: 'Asset updated successfully',
      data: transformedAsset
    });

  } catch (error) {
    console.error('Update asset error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

// Delete asset
router.delete('/:id', authMiddleware, async (req, res) => {
  try {
    const asset = await Asset.findById(req.params.id);
    
    if (!asset) {
      return res.status(404).json({
        success: false,
        message: 'Asset not found'
      });
    }

    // Soft delete by setting isActive to false
    asset.isActive = false;
    await asset.save();

    res.json({
      success: true,
      message: 'Asset deleted successfully'
    });

  } catch (error) {
    console.error('Delete asset error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

export default router;
