import express from 'express';
import { OwnerCompany, SubCompany } from '../models/Company.js';
import { authMiddleware, requireRole } from '../middleware/auth.js';

const router = express.Router();

// Get owner company
router.get('/owner', authMiddleware, async (req, res) => {
  try {
    const ownerCompany = await OwnerCompany.findOne();
    
    if (!ownerCompany) {
      return res.status(404).json({
        success: false,
        message: 'Owner company not found'
      });
    }

    res.json({
      success: true,
      data: ownerCompany
    });

  } catch (error) {
    console.error('Get owner company error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

// Get all sub-companies
router.get('/sub', authMiddleware, async (req, res) => {
  try {
    const subCompanies = await SubCompany.find()
      .populate('ownerCompanyId')
      .populate('adminUserId', '-password')
      .sort({ createdAt: -1 });

    // Transform data to match frontend expectations
    const transformedCompanies = subCompanies.map(company => ({
      id: company._id,
      owner_company_id: company.ownerCompanyId._id,
      name: company.name,
      industry: company.industry,
      description: company.description,
      established_date: company.establishedDate,
      status: company.status,
      logo: company.logo,
      website: company.website,
      contact_email: company.contactEmail,
      contact_phone: company.contactPhone,
      address: company.address,
      created_at: company.createdAt,
      updated_at: company.updatedAt,
      ownerCompany: company.ownerCompanyId ? {
        id: company.ownerCompanyId._id,
        name: company.ownerCompanyId.name,
        address: company.ownerCompanyId.address,
        contact_email: company.ownerCompanyId.contactEmail,
        established_date: company.ownerCompanyId.establishedDate,
        created_at: company.ownerCompanyId.createdAt,
        updated_at: company.ownerCompanyId.updatedAt
      } : null,
      admin: company.adminUserId ? {
        id: company.adminUserId._id,
        email: company.adminUserId.email,
        firstName: company.adminUserId.firstName,
        lastName: company.adminUserId.lastName,
        created_at: company.adminUserId.createdAt,
        updated_at: company.adminUserId.updatedAt
      } : null,
      totalInvestments: 0, // TODO: Calculate from investments
      totalInvestors: 0,   // TODO: Calculate from investor investments
      totalValue: 0,       // TODO: Calculate from investments
      profitLoss: {
        profit: 0,
        loss: 0,
        roi: 0
      }
    }));

    res.json({
      success: true,
      data: transformedCompanies
    });

  } catch (error) {
    console.error('Get sub-companies error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

// Get sub-company by ID
router.get('/sub/:id', authMiddleware, async (req, res) => {
  try {
    const subCompany = await SubCompany.findById(req.params.id)
      .populate('ownerCompanyId')
      .populate('adminUserId', '-password');

    if (!subCompany) {
      return res.status(404).json({
        success: false,
        message: 'Sub-company not found'
      });
    }

    const transformedCompany = {
      id: subCompany._id,
      owner_company_id: subCompany.ownerCompanyId._id,
      name: subCompany.name,
      industry: subCompany.industry,
      description: subCompany.description,
      established_date: subCompany.establishedDate,
      status: subCompany.status,
      logo: subCompany.logo,
      website: subCompany.website,
      contact_email: subCompany.contactEmail,
      contact_phone: subCompany.contactPhone,
      address: subCompany.address,
      created_at: subCompany.createdAt,
      updated_at: subCompany.updatedAt,
      ownerCompany: subCompany.ownerCompanyId ? {
        id: subCompany.ownerCompanyId._id,
        name: subCompany.ownerCompanyId.name,
        address: subCompany.ownerCompanyId.address,
        contact_email: subCompany.ownerCompanyId.contactEmail,
        established_date: subCompany.ownerCompanyId.establishedDate,
        created_at: subCompany.ownerCompanyId.createdAt,
        updated_at: subCompany.ownerCompanyId.updatedAt
      } : null,
      admin: subCompany.adminUserId ? {
        id: subCompany.adminUserId._id,
        email: subCompany.adminUserId.email,
        firstName: subCompany.adminUserId.firstName,
        lastName: subCompany.adminUserId.lastName,
        created_at: subCompany.adminUserId.createdAt,
        updated_at: subCompany.adminUserId.updatedAt
      } : null,
      totalInvestments: 0,
      totalInvestors: 0,
      totalValue: 0,
      profitLoss: {
        profit: 0,
        loss: 0,
        roi: 0
      }
    };

    res.json({
      success: true,
      data: transformedCompany
    });

  } catch (error) {
    console.error('Get sub-company error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

// Create sub-company (superadmin only)
router.post('/sub', authMiddleware, requireRole(['superadmin']), async (req, res) => {
  try {
    const { name, industry, description, adminEmail, adminFirstName, adminLastName } = req.body;

    // Get or create owner company
    let ownerCompany = await OwnerCompany.findOne();
    if (!ownerCompany) {
      ownerCompany = new OwnerCompany({
        name: 'Investment Holdings Corp',
        address: '123 Main St, New York, NY',
        contactEmail: '<EMAIL>',
        establishedDate: new Date('2020-01-01')
      });
      await ownerCompany.save();
    }

    // Create sub-company
    const subCompany = new SubCompany({
      ownerCompanyId: ownerCompany._id,
      name,
      industry,
      description,
      establishedDate: new Date(),
      status: 'active'
    });

    await subCompany.save();

    // Populate the response
    await subCompany.populate('ownerCompanyId');

    const transformedCompany = {
      id: subCompany._id,
      owner_company_id: subCompany.ownerCompanyId._id,
      name: subCompany.name,
      industry: subCompany.industry,
      description: subCompany.description,
      established_date: subCompany.establishedDate,
      status: subCompany.status,
      created_at: subCompany.createdAt,
      updated_at: subCompany.updatedAt,
      ownerCompany: {
        id: subCompany.ownerCompanyId._id,
        name: subCompany.ownerCompanyId.name,
        address: subCompany.ownerCompanyId.address,
        contact_email: subCompany.ownerCompanyId.contactEmail,
        established_date: subCompany.ownerCompanyId.establishedDate,
        created_at: subCompany.ownerCompanyId.createdAt,
        updated_at: subCompany.ownerCompanyId.updatedAt
      },
      totalInvestments: 0,
      totalInvestors: 0,
      totalValue: 0,
      profitLoss: {
        profit: 0,
        loss: 0,
        roi: 0
      }
    };

    res.status(201).json({
      success: true,
      message: 'Sub-company created successfully',
      data: transformedCompany
    });

  } catch (error) {
    console.error('Create sub-company error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

// Update sub-company
router.put('/sub/:id', authMiddleware, requireRole(['superadmin', 'admin']), async (req, res) => {
  try {
    const { name, industry, description, status } = req.body;

    const subCompany = await SubCompany.findById(req.params.id);
    
    if (!subCompany) {
      return res.status(404).json({
        success: false,
        message: 'Sub-company not found'
      });
    }

    // Update fields
    if (name) subCompany.name = name;
    if (industry) subCompany.industry = industry;
    if (description) subCompany.description = description;
    if (status) subCompany.status = status;

    await subCompany.save();
    await subCompany.populate('ownerCompanyId');

    const transformedCompany = {
      id: subCompany._id,
      owner_company_id: subCompany.ownerCompanyId._id,
      name: subCompany.name,
      industry: subCompany.industry,
      description: subCompany.description,
      established_date: subCompany.establishedDate,
      status: subCompany.status,
      created_at: subCompany.createdAt,
      updated_at: subCompany.updatedAt,
      ownerCompany: {
        id: subCompany.ownerCompanyId._id,
        name: subCompany.ownerCompanyId.name,
        address: subCompany.ownerCompanyId.address,
        contact_email: subCompany.ownerCompanyId.contactEmail,
        established_date: subCompany.ownerCompanyId.establishedDate,
        created_at: subCompany.ownerCompanyId.createdAt,
        updated_at: subCompany.ownerCompanyId.updatedAt
      },
      totalInvestments: 0,
      totalInvestors: 0,
      totalValue: 0,
      profitLoss: {
        profit: 0,
        loss: 0,
        roi: 0
      }
    };

    res.json({
      success: true,
      message: 'Sub-company updated successfully',
      data: transformedCompany
    });

  } catch (error) {
    console.error('Update sub-company error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

// Delete sub-company
router.delete('/sub/:id', authMiddleware, requireRole(['superadmin']), async (req, res) => {
  try {
    const subCompany = await SubCompany.findById(req.params.id);
    
    if (!subCompany) {
      return res.status(404).json({
        success: false,
        message: 'Sub-company not found'
      });
    }

    await SubCompany.findByIdAndDelete(req.params.id);

    res.json({
      success: true,
      message: 'Sub-company deleted successfully'
    });

  } catch (error) {
    console.error('Delete sub-company error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

export default router;
