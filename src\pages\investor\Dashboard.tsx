import React, { useEffect } from 'react';
import { PieChartIcon, TrendingUpIcon, DollarSignIcon, PercentIcon, ArrowRightIcon } from 'lucide-react';
import DashboardLayout from '../../layouts/DashboardLayout';
import StatCard from '../../components/dashboard/StatCard';
import PerformanceChart from '../../components/dashboard/PerformanceChart';
import Button from '../../components/ui/Button';
import { useNavigate } from 'react-router-dom';
import { useInvestment } from '../../contexts/InvestmentContext';
const InvestorDashboard: React.FC = () => {
  const navigate = useNavigate();
  const {
    investments,
    fetchInvestments
  } = useInvestment();
  useEffect(() => {
    fetchInvestments();
  }, []);
  // Sample portfolio data - in a real app this would come from an API
  const portfolioSummary = {
    totalInvestment: 175000,
    currentValue: 203000,
    totalProfit: 28000,
    roi: 16,
    changeToday: 0.8
  };
  // Performance chart data
  const performanceData = [{
    name: 'Jan',
    value: 165000,
    benchmark: 160000
  }, {
    name: 'Feb',
    value: 172000,
    benchmark: 165000
  }, {
    name: 'Mar',
    value: 168000,
    benchmark: 168000
  }, {
    name: 'Apr',
    value: 178000,
    benchmark: 172000
  }, {
    name: 'May',
    value: 192000,
    benchmark: 180000
  }, {
    name: 'Jun',
    value: 203000,
    benchmark: 185000
  }];
  // Opportunities preview - in a real app these would be fetched from an API
  const opportunities = [{
    id: 'opp1',
    name: 'Tech Growth Fund',
    category: 'Technology',
    targetReturn: 22,
    minInvestment: 10000
  }, {
    id: 'opp2',
    name: 'Healthcare Innovation',
    category: 'Healthcare',
    targetReturn: 18,
    minInvestment: 25000
  }, {
    id: 'opp3',
    name: 'Renewable Energy',
    category: 'Energy',
    targetReturn: 15,
    minInvestment: 15000
  }];
  return <DashboardLayout title="Investor Dashboard" subtitle="Overview of your investments and opportunities">
      {/* Top stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
        <StatCard title="Total Investment" value={`$${portfolioSummary.totalInvestment.toLocaleString()}`} icon={<DollarSignIcon size={24} className="text-blue-500" />} />
        <StatCard title="Current Value" value={`$${portfolioSummary.currentValue.toLocaleString()}`} icon={<TrendingUpIcon size={24} className="text-green-500" />} trend={portfolioSummary.changeToday} trendLabel="today" />
        <StatCard title="Total Profit" value={`$${portfolioSummary.totalProfit.toLocaleString()}`} icon={<DollarSignIcon size={24} className="text-green-500" />} />
        <StatCard title="ROI" value={`${portfolioSummary.roi}%`} icon={<PercentIcon size={24} className="text-yellow-500" />} />
      </div>
      {/* Middle section */}
      <div className="mb-6">
        <PerformanceChart title="Portfolio Performance (6 Months)" data={performanceData} className="bg-gradient-to-br from-slate-800 to-slate-800/80" />
      </div>
      {/* Bottom section */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2 bg-slate-800 rounded-lg p-6">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-medium text-white">My Investments</h3>
            <Button variant="secondary" size="sm" onClick={() => navigate('/investor/portfolio')}>
              View Portfolio
            </Button>
          </div>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="text-left text-sm text-slate-400 border-b border-slate-700">
                  <th className="pb-3 font-medium">Name</th>
                  <th className="pb-3 font-medium">Type</th>
                  <th className="pb-3 font-medium">Amount</th>
                  <th className="pb-3 font-medium">ROI</th>
                  <th className="pb-3 font-medium">Status</th>
                </tr>
              </thead>
              <tbody>
                {investments.length > 0 ? investments.slice(0, 4).map(inv => <tr key={inv.id} className="border-b border-slate-700">
                      <td className="py-3 text-white">{inv.name}</td>
                      <td className="py-3 text-slate-300">{inv.type}</td>
                      <td className="py-3 text-white">
                        ${inv.amount.toLocaleString()}
                      </td>
                      <td className="py-3 text-yellow-500">{inv.roi}%</td>
                      <td className="py-3">
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${inv.status === 'Performing' ? 'bg-green-500/20 text-green-500' : inv.status === 'At Risk' ? 'bg-yellow-500/20 text-yellow-500' : 'bg-blue-500/20 text-blue-500'}`}>
                          {inv.status}
                        </span>
                      </td>
                    </tr>) : <tr>
                    <td colSpan={5} className="py-4 text-center text-slate-400">
                      No investments found
                    </td>
                  </tr>}
              </tbody>
            </table>
          </div>
        </div>
        <div className="bg-slate-800 rounded-lg p-6">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-medium text-white">
              New Opportunities
            </h3>
            <Button variant="secondary" size="sm" onClick={() => navigate('/investor/marketplace')}>
              View All
            </Button>
          </div>
          <div className="space-y-4">
            {opportunities.map(opp => <div key={opp.id} className="bg-slate-700 p-4 rounded-lg">
                <div className="flex justify-between items-start mb-2">
                  <h4 className="font-medium text-white">{opp.name}</h4>
                  <span className="px-2 py-1 rounded-full text-xs font-medium bg-blue-500/20 text-blue-500">
                    {opp.category}
                  </span>
                </div>
                <div className="grid grid-cols-2 gap-2 mb-3">
                  <div>
                    <p className="text-xs text-slate-400">Target Return</p>
                    <p className="text-yellow-500 font-medium">
                      {opp.targetReturn}%
                    </p>
                  </div>
                  <div>
                    <p className="text-xs text-slate-400">Min Investment</p>
                    <p className="text-white font-medium">
                      ${opp.minInvestment.toLocaleString()}
                    </p>
                  </div>
                </div>
                <Button variant="secondary" size="sm" fullWidth onClick={() => navigate('/investor/marketplace')} className="flex items-center justify-center">
                  View Details
                  <ArrowRightIcon size={16} className="ml-2" />
                </Button>
              </div>)}
          </div>
        </div>
      </div>
    </DashboardLayout>;
};
export default InvestorDashboard;