import React, { useEffect, useState } from 'react';
import { PlusIcon, SearchIcon, FilterIcon, UsersIcon, UserPlusIcon, MailIcon, PhoneIcon, CheckCircleIcon, XCircleIcon, EyeIcon } from 'lucide-react';
import DashboardLayout from '../../layouts/DashboardLayout';
import Button from '../../components/ui/Button';
import { useAuth } from '../../contexts/AuthContext';
import InvestorDetailModal from '../../components/admin/InvestorDetailModal';
interface Investor {
  id: string;
  name: string;
  email: string;
  phone: string;
  investmentTotal: number;
  investmentCount: number;
  status: 'active' | 'inactive' | 'pending' | 'rejected';
  joinDate: string;
  lastActivity: string;
  documents?: {
    id: string;
    name: string;
    type: string;
    uploadDate: string;
    verified: boolean;
  }[];
}
const Investors: React.FC = () => {
  const {
    user,
    updateUserStatus
  } = useAuth();
  const [loading, setLoading] = useState(true);
  const [investors, setInvestors] = useState<Investor[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [filter, setFilter] = useState('all');
  const [selectedInvestor, setSelectedInvestor] = useState<Investor | null>(null);
  const [processingId, setProcessingId] = useState<string | null>(null);
  useEffect(() => {
    // Simulate API call to fetch investors
    setTimeout(() => {
      // Mock data
      const mockInvestors: Investor[] = [{
        id: 'inv-001',
        name: 'Michael Johnson',
        email: '<EMAIL>',
        phone: '+****************',
        investmentTotal: 250000,
        investmentCount: 3,
        status: 'active',
        joinDate: '2023-01-15',
        lastActivity: '2023-05-20',
        documents: [{
          id: 'doc1',
          name: 'ID Verification',
          type: 'identification',
          uploadDate: '2023-01-10',
          verified: true
        }, {
          id: 'doc2',
          name: 'Proof of Address',
          type: 'address',
          uploadDate: '2023-01-10',
          verified: true
        }]
      }, {
        id: 'inv-002',
        name: 'Sarah Williams',
        email: '<EMAIL>',
        phone: '+****************',
        investmentTotal: 180000,
        investmentCount: 2,
        status: 'active',
        joinDate: '2023-02-08',
        lastActivity: '2023-05-18',
        documents: [{
          id: 'doc3',
          name: 'ID Verification',
          type: 'identification',
          uploadDate: '2023-02-01',
          verified: true
        }, {
          id: 'doc4',
          name: 'Proof of Address',
          type: 'address',
          uploadDate: '2023-02-01',
          verified: true
        }]
      }, {
        id: 'inv-003',
        name: 'David Chen',
        email: '<EMAIL>',
        phone: '+****************',
        investmentTotal: 320000,
        investmentCount: 4,
        status: 'active',
        joinDate: '2022-11-22',
        lastActivity: '2023-05-15',
        documents: [{
          id: 'doc5',
          name: 'ID Verification',
          type: 'identification',
          uploadDate: '2022-11-15',
          verified: true
        }, {
          id: 'doc6',
          name: 'Proof of Address',
          type: 'address',
          uploadDate: '2022-11-15',
          verified: true
        }]
      }, {
        id: 'inv-004',
        name: 'Emily Rodriguez',
        email: '<EMAIL>',
        phone: '+****************',
        investmentTotal: 0,
        investmentCount: 0,
        status: 'pending',
        joinDate: '2023-04-30',
        lastActivity: '2023-05-01',
        documents: [{
          id: 'doc7',
          name: 'ID Verification',
          type: 'identification',
          uploadDate: '2023-04-30',
          verified: false
        }, {
          id: 'doc8',
          name: 'Proof of Address',
          type: 'address',
          uploadDate: '2023-04-30',
          verified: false
        }, {
          id: 'doc9',
          name: 'Financial Statement',
          type: 'financial',
          uploadDate: '2023-04-30',
          verified: false
        }]
      }, {
        id: 'inv-005',
        name: 'Robert Kim',
        email: '<EMAIL>',
        phone: '+****************',
        investmentTotal: 420000,
        investmentCount: 5,
        status: 'active',
        joinDate: '2022-09-15',
        lastActivity: '2023-05-10',
        documents: [{
          id: 'doc10',
          name: 'ID Verification',
          type: 'identification',
          uploadDate: '2022-09-10',
          verified: true
        }, {
          id: 'doc11',
          name: 'Proof of Address',
          type: 'address',
          uploadDate: '2022-09-10',
          verified: true
        }]
      }, {
        id: 'inv-006',
        name: 'Jessica Patel',
        email: '<EMAIL>',
        phone: '+****************',
        investmentTotal: 150000,
        investmentCount: 2,
        status: 'inactive',
        joinDate: '2022-12-10',
        lastActivity: '2023-03-25',
        documents: [{
          id: 'doc12',
          name: 'ID Verification',
          type: 'identification',
          uploadDate: '2022-12-05',
          verified: true
        }, {
          id: 'doc13',
          name: 'Proof of Address',
          type: 'address',
          uploadDate: '2022-12-05',
          verified: true
        }]
      }, {
        id: 'inv-007',
        name: 'Thomas Wilson',
        email: '<EMAIL>',
        phone: '+****************',
        investmentTotal: 0,
        investmentCount: 0,
        status: 'pending',
        joinDate: '2023-05-18',
        lastActivity: '2023-05-18',
        documents: [{
          id: 'doc14',
          name: 'ID Verification',
          type: 'identification',
          uploadDate: '2023-05-18',
          verified: false
        }, {
          id: 'doc15',
          name: 'Proof of Address',
          type: 'address',
          uploadDate: '2023-05-18',
          verified: false
        }]
      }];
      setInvestors(mockInvestors);
      setLoading(false);
    }, 1000);
  }, []);
  const filteredInvestors = investors.filter(investor => {
    if (filter === 'all') return true;
    return investor.status === filter;
  }).filter(investor => investor.name.toLowerCase().includes(searchTerm.toLowerCase()) || investor.email.toLowerCase().includes(searchTerm.toLowerCase()));
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-500/20 text-green-500';
      case 'inactive':
        return 'bg-slate-500/20 text-slate-500';
      case 'pending':
        return 'bg-yellow-500/20 text-yellow-500';
      case 'rejected':
        return 'bg-red-500/20 text-red-500';
      default:
        return 'bg-slate-500/20 text-slate-500';
    }
  };
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };
  const handleViewDetails = (investor: Investor) => {
    setSelectedInvestor(investor);
  };
  const handleApproveInvestor = async (id: string) => {
    try {
      setProcessingId(id);
      await updateUserStatus(id, 'active');
      // Update local state
      setInvestors(prev => prev.map(investor => investor.id === id ? {
        ...investor,
        status: 'active'
      } : investor));
    } catch (error) {
      console.error('Error approving investor:', error);
    } finally {
      setProcessingId(null);
    }
  };
  const handleRejectInvestor = async (id: string) => {
    try {
      setProcessingId(id);
      await updateUserStatus(id, 'rejected');
      // Update local state
      setInvestors(prev => prev.map(investor => investor.id === id ? {
        ...investor,
        status: 'rejected'
      } : investor));
    } catch (error) {
      console.error('Error rejecting investor:', error);
    } finally {
      setProcessingId(null);
    }
  };
  const handleCloseModal = () => {
    setSelectedInvestor(null);
  };
  return <DashboardLayout title="Investors" subtitle={`Manage investors for ${user?.companyName || 'your company'}`}>
      <div className="mb-6 flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <div className="flex items-center gap-4">
          <div className="relative">
            <input type="text" placeholder="Search investors..." value={searchTerm} onChange={e => setSearchTerm(e.target.value)} className="w-full md:w-64 bg-slate-700 border border-slate-600 rounded-md py-2 pl-10 pr-4 text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-yellow-500" />
            <SearchIcon className="absolute left-3 top-2.5 h-5 w-5 text-slate-400" />
          </div>
          <div className="relative">
            <select value={filter} onChange={e => setFilter(e.target.value)} className="appearance-none bg-slate-700 border border-slate-600 rounded-md py-2 pl-4 pr-10 text-white focus:outline-none focus:ring-2 focus:ring-yellow-500">
              <option value="all">All Status</option>
              <option value="active">Active</option>
              <option value="pending">Pending</option>
              <option value="inactive">Inactive</option>
              <option value="rejected">Rejected</option>
            </select>
            <FilterIcon className="absolute right-3 top-2.5 h-5 w-5 text-slate-400 pointer-events-none" />
          </div>
        </div>
        <Button variant="primary" className="flex items-center justify-center">
          <UserPlusIcon className="h-5 w-5 mr-2" />
          Add Investor
        </Button>
      </div>
      {/* Pending Approval Alert */}
      {filteredInvestors.some(investor => investor.status === 'pending') && <div className="mb-6 bg-yellow-500/10 border border-yellow-500/20 rounded-lg p-4">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <UsersIcon className="h-5 w-5 text-yellow-500" />
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-yellow-500">
                Pending Approvals
              </h3>
              <div className="mt-1 text-sm text-slate-300">
                <p>
                  {filteredInvestors.filter(inv => inv.status === 'pending').length}{' '}
                  investor(s) waiting for approval. Please review their
                  information and documentation.
                </p>
              </div>
            </div>
          </div>
        </div>}
      <div className="bg-slate-800 rounded-lg overflow-hidden shadow-lg">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="text-left text-sm text-slate-400 border-b border-slate-700">
                <th className="p-4 font-medium">Investor</th>
                <th className="p-4 font-medium">Contact</th>
                <th className="p-4 font-medium">Total Investment</th>
                <th className="p-4 font-medium"># Investments</th>
                <th className="p-4 font-medium">Join Date</th>
                <th className="p-4 font-medium">Status</th>
                <th className="p-4 font-medium">Actions</th>
              </tr>
            </thead>
            <tbody>
              {loading ? [...Array(3)].map((_, i) => <tr key={i} className="animate-pulse border-b border-slate-700">
                    <td className="p-4">
                      <div className="flex items-center">
                        <div className="h-10 w-10 rounded-full bg-slate-700"></div>
                        <div className="ml-3">
                          <div className="h-4 w-24 bg-slate-700 rounded"></div>
                          <div className="h-3 w-16 bg-slate-700 rounded mt-1"></div>
                        </div>
                      </div>
                    </td>
                    <td className="p-4">
                      <div className="h-4 w-32 bg-slate-700 rounded"></div>
                    </td>
                    <td className="p-4">
                      <div className="h-4 w-24 bg-slate-700 rounded"></div>
                    </td>
                    <td className="p-4">
                      <div className="h-4 w-12 bg-slate-700 rounded"></div>
                    </td>
                    <td className="p-4">
                      <div className="h-4 w-20 bg-slate-700 rounded"></div>
                    </td>
                    <td className="p-4">
                      <div className="h-6 w-16 bg-slate-700 rounded"></div>
                    </td>
                    <td className="p-4">
                      <div className="h-8 w-20 bg-slate-700 rounded"></div>
                    </td>
                  </tr>) : filteredInvestors.length > 0 ? filteredInvestors.map(investor => <tr key={investor.id} className={`border-b border-slate-700 ${investor.status === 'pending' ? 'bg-yellow-500/5' : ''} hover:bg-slate-700/50`}>
                    <td className="p-4">
                      <div className="flex items-center">
                        <div className="h-10 w-10 rounded-full bg-slate-700 flex items-center justify-center text-white font-medium">
                          {investor.name.charAt(0)}
                        </div>
                        <div className="ml-3">
                          <div className="font-medium text-white">
                            {investor.name}
                          </div>
                          <div className="text-sm text-slate-400">
                            ID: {investor.id}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="p-4">
                      <div className="space-y-1">
                        <div className="flex items-center text-slate-300">
                          <MailIcon className="h-4 w-4 mr-2 text-slate-400" />
                          {investor.email}
                        </div>
                        <div className="flex items-center text-slate-300">
                          <PhoneIcon className="h-4 w-4 mr-2 text-slate-400" />
                          {investor.phone}
                        </div>
                      </div>
                    </td>
                    <td className="p-4 text-white">
                      ${investor.investmentTotal.toLocaleString()}
                    </td>
                    <td className="p-4 text-white">
                      {investor.investmentCount}
                    </td>
                    <td className="p-4 text-slate-300">
                      {formatDate(investor.joinDate)}
                    </td>
                    <td className="p-4">
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(investor.status)}`}>
                        {investor.status.charAt(0).toUpperCase() + investor.status.slice(1)}
                      </span>
                    </td>
                    <td className="p-4">
                      <div className="flex space-x-2">
                        <Button variant="secondary" size="sm" onClick={() => handleViewDetails(investor)}>
                          <EyeIcon className="h-4 w-4 mr-1" />
                          View
                        </Button>
                        {investor.status === 'pending' && <>
                            <Button variant="success" size="sm" onClick={() => handleApproveInvestor(investor.id)} isLoading={processingId === investor.id} disabled={!!processingId}>
                              {processingId !== investor.id && <CheckCircleIcon className="h-4 w-4 mr-1" />}
                              Approve
                            </Button>
                            <Button variant="danger" size="sm" onClick={() => handleRejectInvestor(investor.id)} isLoading={processingId === investor.id} disabled={!!processingId}>
                              {processingId !== investor.id && <XCircleIcon className="h-4 w-4 mr-1" />}
                              Reject
                            </Button>
                          </>}
                      </div>
                    </td>
                  </tr>) : <tr>
                  <td colSpan={7} className="p-8 text-center text-slate-400">
                    <UsersIcon className="h-12 w-12 mx-auto mb-2 text-slate-500" />
                    <p>No investors found matching your criteria</p>
                  </td>
                </tr>}
            </tbody>
          </table>
        </div>
      </div>
      <div className="mt-6 grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-slate-800 p-6 rounded-lg shadow-lg">
          <h3 className="text-lg font-medium text-white mb-4 flex items-center">
            <UsersIcon className="h-5 w-5 text-blue-500 mr-2" />
            Investor Overview
          </h3>
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <span className="text-slate-400">Total Investors:</span>
              <span className="text-white font-medium">{investors.length}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-slate-400">Active Investors:</span>
              <span className="text-green-500 font-medium">
                {investors.filter(inv => inv.status === 'active').length}
              </span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-slate-400">Pending Approval:</span>
              <span className="text-yellow-500 font-medium">
                {investors.filter(inv => inv.status === 'pending').length}
              </span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-slate-400">Inactive Investors:</span>
              <span className="text-slate-300 font-medium">
                {investors.filter(inv => inv.status === 'inactive').length}
              </span>
            </div>
          </div>
        </div>
        <div className="bg-slate-800 p-6 rounded-lg shadow-lg">
          <h3 className="text-lg font-medium text-white mb-4 flex items-center">
            <PlusIcon className="h-5 w-5 text-green-500 mr-2" />
            New Investors
          </h3>
          <div className="space-y-3">
            {investors.filter(inv => {
            const joinDate = new Date(inv.joinDate);
            const thirtyDaysAgo = new Date();
            thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
            return joinDate >= thirtyDaysAgo;
          }).slice(0, 3).map(inv => <div key={inv.id} className="flex items-center p-3 bg-slate-700 rounded-lg">
                  <div className="h-8 w-8 rounded-full bg-slate-600 flex items-center justify-center text-white font-medium mr-3">
                    {inv.name.charAt(0)}
                  </div>
                  <div>
                    <div className="text-white font-medium">{inv.name}</div>
                    <div className="text-xs text-slate-400">
                      Joined {formatDate(inv.joinDate)}
                    </div>
                  </div>
                </div>)}
            {investors.filter(inv => {
            const joinDate = new Date(inv.joinDate);
            const thirtyDaysAgo = new Date();
            thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
            return joinDate >= thirtyDaysAgo;
          }).length === 0 && <div className="text-center py-4 text-slate-400">
                No new investors in the last 30 days
              </div>}
          </div>
        </div>
        <div className="bg-slate-800 p-6 rounded-lg shadow-lg">
          <h3 className="text-lg font-medium text-white mb-4 flex items-center">
            <UsersIcon className="h-5 w-5 text-yellow-500 mr-2" />
            Top Investors
          </h3>
          <div className="space-y-3">
            {[...investors].sort((a, b) => b.investmentTotal - a.investmentTotal).slice(0, 3).map((inv, index) => <div key={inv.id} className="flex items-center justify-between p-3 bg-slate-700 rounded-lg">
                  <div className="flex items-center">
                    <div className="h-8 w-8 rounded-full bg-slate-600 flex items-center justify-center text-white font-medium mr-3">
                      {index + 1}
                    </div>
                    <div className="text-white font-medium">{inv.name}</div>
                  </div>
                  <div className="text-yellow-500 font-medium">
                    ${inv.investmentTotal.toLocaleString()}
                  </div>
                </div>)}
          </div>
        </div>
      </div>
      {/* Investor Detail Modal */}
      {selectedInvestor && <InvestorDetailModal investor={selectedInvestor} onClose={handleCloseModal} onApprove={handleApproveInvestor} onReject={handleRejectInvestor} isProcessing={processingId === selectedInvestor.id} />}
    </DashboardLayout>;
};
export default Investors;