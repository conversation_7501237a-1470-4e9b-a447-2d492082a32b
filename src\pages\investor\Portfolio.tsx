import React, { useState } from 'react';
import { PieChartIcon, TrendingUpIcon, TrendingDownIcon, DollarSignIcon, PercentIcon } from 'lucide-react';
import DashboardLayout from '../../layouts/DashboardLayout';
import Button from '../../components/ui/Button';
const Portfolio: React.FC = () => {
  const [timeframe, setTimeframe] = useState('all');
  // Sample portfolio data
  const portfolioSummary = {
    totalInvestment: 250000,
    currentValue: 312500,
    totalProfit: 62500,
    roi: 25,
    changeToday: 1.2
  };
  const investments = [{
    id: 'inv-001',
    name: 'Tech Growth Fund',
    initialInvestment: 75000,
    currentValue: 96000,
    roi: 28,
    change: 2.3,
    status: 'performing',
    category: 'Technology'
  }, {
    id: 'inv-002',
    name: 'Healthcare Innovation',
    initialInvestment: 50000,
    currentValue: 58500,
    roi: 17,
    change: 0.8,
    status: 'performing',
    category: 'Healthcare'
  }, {
    id: 'inv-003',
    name: 'Renewable Energy Project',
    initialInvestment: 40000,
    currentValue: 44800,
    roi: 12,
    change: -0.4,
    status: 'underperforming',
    category: 'Energy'
  }, {
    id: 'inv-004',
    name: 'Real Estate Development',
    initialInvestment: 60000,
    currentValue: 81000,
    roi: 35,
    change: 1.5,
    status: 'performing',
    category: 'Real Estate'
  }, {
    id: 'inv-005',
    name: 'Fintech Startup',
    initialInvestment: 25000,
    currentValue: 32200,
    roi: 29,
    change: 2.1,
    status: 'performing',
    category: 'Finance'
  }];
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'performing':
        return 'bg-green-500/20 text-green-500';
      case 'underperforming':
        return 'bg-yellow-500/20 text-yellow-500';
      case 'loss':
        return 'bg-red-500/20 text-red-500';
      default:
        return 'bg-slate-500/20 text-slate-500';
    }
  };
  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'Technology':
        return 'bg-blue-500/20 text-blue-500';
      case 'Healthcare':
        return 'bg-green-500/20 text-green-500';
      case 'Energy':
        return 'bg-yellow-500/20 text-yellow-500';
      case 'Real Estate':
        return 'bg-purple-500/20 text-purple-500';
      case 'Finance':
        return 'bg-teal-500/20 text-teal-500';
      default:
        return 'bg-slate-500/20 text-slate-500';
    }
  };
  return <DashboardLayout title="My Portfolio" subtitle="Track and manage your investment portfolio">
      {/* Portfolio Summary */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
        <div className="bg-slate-800 p-6 rounded-lg">
          <div className="flex items-center justify-between mb-2">
            <h3 className="text-sm font-medium text-slate-400">
              Total Investment
            </h3>
            <DollarSignIcon className="h-5 w-5 text-yellow-500" />
          </div>
          <p className="text-2xl font-bold text-white">
            ${portfolioSummary.totalInvestment.toLocaleString()}
          </p>
        </div>
        <div className="bg-slate-800 p-6 rounded-lg">
          <div className="flex items-center justify-between mb-2">
            <h3 className="text-sm font-medium text-slate-400">
              Current Value
            </h3>
            <TrendingUpIcon className="h-5 w-5 text-green-500" />
          </div>
          <p className="text-2xl font-bold text-white">
            ${portfolioSummary.currentValue.toLocaleString()}
          </p>
        </div>
        <div className="bg-slate-800 p-6 rounded-lg">
          <div className="flex items-center justify-between mb-2">
            <h3 className="text-sm font-medium text-slate-400">Total Profit</h3>
            <DollarSignIcon className="h-5 w-5 text-green-500" />
          </div>
          <p className="text-2xl font-bold text-green-500">
            +${portfolioSummary.totalProfit.toLocaleString()}
          </p>
        </div>
        <div className="bg-slate-800 p-6 rounded-lg">
          <div className="flex items-center justify-between mb-2">
            <h3 className="text-sm font-medium text-slate-400">ROI</h3>
            <PercentIcon className="h-5 w-5 text-yellow-500" />
          </div>
          <p className="text-2xl font-bold text-yellow-500">
            +{portfolioSummary.roi}%
          </p>
          <p className="text-xs text-slate-400 mt-1">
            <span className={portfolioSummary.changeToday > 0 ? 'text-green-500' : 'text-red-500'}>
              {portfolioSummary.changeToday > 0 ? '+' : ''}
              {portfolioSummary.changeToday}%
            </span>{' '}
            today
          </p>
        </div>
      </div>
      {/* Portfolio Chart & Breakdown */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
        <div className="lg:col-span-2 bg-slate-800 p-6 rounded-lg">
          <div className="flex justify-between items-center mb-6">
            <h3 className="text-lg font-medium text-white">
              Portfolio Performance
            </h3>
            <div className="flex space-x-2">
              <Button size="sm" variant={timeframe === 'week' ? 'primary' : 'secondary'} onClick={() => setTimeframe('week')}>
                1W
              </Button>
              <Button size="sm" variant={timeframe === 'month' ? 'primary' : 'secondary'} onClick={() => setTimeframe('month')}>
                1M
              </Button>
              <Button size="sm" variant={timeframe === 'year' ? 'primary' : 'secondary'} onClick={() => setTimeframe('year')}>
                1Y
              </Button>
              <Button size="sm" variant={timeframe === 'all' ? 'primary' : 'secondary'} onClick={() => setTimeframe('all')}>
                All
              </Button>
            </div>
          </div>
          <div className="h-64 flex items-end space-x-2">
            {/* Simple chart visualization */}
            <div className="flex-1 h-full flex items-end">
              <div className="w-full bg-gradient-to-t from-yellow-500/80 to-yellow-500/20 rounded-t-sm" style={{
              height: '60%'
            }}></div>
            </div>
            <div className="flex-1 h-full flex items-end">
              <div className="w-full bg-gradient-to-t from-yellow-500/80 to-yellow-500/20 rounded-t-sm" style={{
              height: '65%'
            }}></div>
            </div>
            <div className="flex-1 h-full flex items-end">
              <div className="w-full bg-gradient-to-t from-yellow-500/80 to-yellow-500/20 rounded-t-sm" style={{
              height: '58%'
            }}></div>
            </div>
            <div className="flex-1 h-full flex items-end">
              <div className="w-full bg-gradient-to-t from-yellow-500/80 to-yellow-500/20 rounded-t-sm" style={{
              height: '70%'
            }}></div>
            </div>
            <div className="flex-1 h-full flex items-end">
              <div className="w-full bg-gradient-to-t from-yellow-500/80 to-yellow-500/20 rounded-t-sm" style={{
              height: '72%'
            }}></div>
            </div>
            <div className="flex-1 h-full flex items-end">
              <div className="w-full bg-gradient-to-t from-yellow-500/80 to-yellow-500/20 rounded-t-sm" style={{
              height: '75%'
            }}></div>
            </div>
            <div className="flex-1 h-full flex items-end">
              <div className="w-full bg-gradient-to-t from-yellow-500/80 to-yellow-500/20 rounded-t-sm" style={{
              height: '85%'
            }}></div>
            </div>
            <div className="flex-1 h-full flex items-end">
              <div className="w-full bg-gradient-to-t from-yellow-500/80 to-yellow-500/20 rounded-t-sm" style={{
              height: '78%'
            }}></div>
            </div>
            <div className="flex-1 h-full flex items-end">
              <div className="w-full bg-gradient-to-t from-yellow-500/80 to-yellow-500/20 rounded-t-sm" style={{
              height: '82%'
            }}></div>
            </div>
            <div className="flex-1 h-full flex items-end">
              <div className="w-full bg-gradient-to-t from-yellow-500/80 to-yellow-500/20 rounded-t-sm" style={{
              height: '90%'
            }}></div>
            </div>
          </div>
        </div>
        <div className="bg-slate-800 p-6 rounded-lg">
          <div className="flex items-center mb-6">
            <PieChartIcon className="h-5 w-5 text-yellow-500 mr-2" />
            <h3 className="text-lg font-medium text-white">
              Portfolio Breakdown
            </h3>
          </div>
          <div className="space-y-4">
            <div>
              <div className="flex justify-between mb-1">
                <span className="text-sm text-slate-400">Technology</span>
                <span className="text-sm text-white">30%</span>
              </div>
              <div className="w-full h-2 bg-slate-700 rounded-full overflow-hidden">
                <div className="bg-blue-500 h-full" style={{
                width: '30%'
              }}></div>
              </div>
            </div>
            <div>
              <div className="flex justify-between mb-1">
                <span className="text-sm text-slate-400">Healthcare</span>
                <span className="text-sm text-white">20%</span>
              </div>
              <div className="w-full h-2 bg-slate-700 rounded-full overflow-hidden">
                <div className="bg-green-500 h-full" style={{
                width: '20%'
              }}></div>
              </div>
            </div>
            <div>
              <div className="flex justify-between mb-1">
                <span className="text-sm text-slate-400">Energy</span>
                <span className="text-sm text-white">16%</span>
              </div>
              <div className="w-full h-2 bg-slate-700 rounded-full overflow-hidden">
                <div className="bg-yellow-500 h-full" style={{
                width: '16%'
              }}></div>
              </div>
            </div>
            <div>
              <div className="flex justify-between mb-1">
                <span className="text-sm text-slate-400">Real Estate</span>
                <span className="text-sm text-white">24%</span>
              </div>
              <div className="w-full h-2 bg-slate-700 rounded-full overflow-hidden">
                <div className="bg-purple-500 h-full" style={{
                width: '24%'
              }}></div>
              </div>
            </div>
            <div>
              <div className="flex justify-between mb-1">
                <span className="text-sm text-slate-400">Finance</span>
                <span className="text-sm text-white">10%</span>
              </div>
              <div className="w-full h-2 bg-slate-700 rounded-full overflow-hidden">
                <div className="bg-teal-500 h-full" style={{
                width: '10%'
              }}></div>
              </div>
            </div>
          </div>
        </div>
      </div>
      {/* Investments List */}
      <div className="bg-slate-800 rounded-lg overflow-hidden">
        <div className="p-6 border-b border-slate-700">
          <h3 className="text-lg font-medium text-white">My Investments</h3>
          <p className="text-sm text-slate-400 mt-1">
            Showing {investments.length} active investments
          </p>
        </div>
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="text-left text-sm text-slate-400 border-b border-slate-700">
                <th className="p-4 font-medium">Investment</th>
                <th className="p-4 font-medium">Category</th>
                <th className="p-4 font-medium">Initial</th>
                <th className="p-4 font-medium">Current Value</th>
                <th className="p-4 font-medium">ROI</th>
                <th className="p-4 font-medium">Change</th>
                <th className="p-4 font-medium">Status</th>
                <th className="p-4 font-medium">Actions</th>
              </tr>
            </thead>
            <tbody>
              {investments.map(investment => <tr key={investment.id} className="border-b border-slate-700 hover:bg-slate-700/50">
                  <td className="p-4 text-white font-medium">
                    {investment.name}
                  </td>
                  <td className="p-4">
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${getCategoryColor(investment.category)}`}>
                      {investment.category}
                    </span>
                  </td>
                  <td className="p-4 text-white">
                    ${investment.initialInvestment.toLocaleString()}
                  </td>
                  <td className="p-4 text-white">
                    ${investment.currentValue.toLocaleString()}
                  </td>
                  <td className="p-4 text-yellow-500">+{investment.roi}%</td>
                  <td className="p-4">
                    <div className="flex items-center">
                      {investment.change > 0 ? <TrendingUpIcon className="h-4 w-4 text-green-500 mr-1" /> : <TrendingDownIcon className="h-4 w-4 text-red-500 mr-1" />}
                      <span className={investment.change > 0 ? 'text-green-500' : 'text-red-500'}>
                        {investment.change > 0 ? '+' : ''}
                        {investment.change}%
                      </span>
                    </div>
                  </td>
                  <td className="p-4">
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(investment.status)}`}>
                      {investment.status.charAt(0).toUpperCase() + investment.status.slice(1)}
                    </span>
                  </td>
                  <td className="p-4">
                    <Button variant="secondary" size="sm">
                      Details
                    </Button>
                  </td>
                </tr>)}
            </tbody>
          </table>
        </div>
      </div>
    </DashboardLayout>;
};
export default Portfolio;