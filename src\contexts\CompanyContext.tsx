import React, { useState, createContext, useContext } from 'react';
interface Company {
  id: string;
  name: string;
  industry: string;
  adminId: string;
  joinDate: string;
  logo: string;
  performance: {
    profit: number;
    loss: number;
    roi: number;
  };
  status: 'active' | 'inactive' | 'pending';
}
interface CompanyContextType {
  companies: Company[];
  loading: boolean;
  fetchCompanies: () => Promise<void>;
  getCompanyById: (id: string) => Company | undefined;
}
const CompanyContext = createContext<CompanyContextType>({
  companies: [],
  loading: false,
  fetchCompanies: async () => {},
  getCompanyById: () => undefined
});
export const useCompany = () => useContext(CompanyContext);
export const CompanyProvider: React.FC<{
  children: React.ReactNode;
}> = ({
  children
}) => {
  const [companies, setCompanies] = useState<Company[]>([]);
  const [loading, setLoading] = useState(false);
  const fetchCompanies = async () => {
    setLoading(true);
    try {
      // This would be an API call in a real application
      // Simulating API response for demo
      const mockCompanies: Company[] = [{
        id: 'comp1',
        name: 'TechVest Inc.',
        industry: 'Technology',
        adminId: 'user1',
        joinDate: '2023-01-15',
        logo: 'https://via.placeholder.com/150',
        performance: {
          profit: 125000,
          loss: 45000,
          roi: 17.5
        },
        status: 'active'
      }, {
        id: 'comp2',
        name: 'GreenEnergy Fund',
        industry: 'Energy',
        adminId: 'user2',
        joinDate: '2023-03-22',
        logo: 'https://via.placeholder.com/150',
        performance: {
          profit: 89000,
          loss: 23000,
          roi: 12.8
        },
        status: 'active'
      }, {
        id: 'comp3',
        name: 'MediLife Ventures',
        industry: 'Healthcare',
        adminId: 'user3',
        joinDate: '2023-05-10',
        logo: 'https://via.placeholder.com/150',
        performance: {
          profit: 67000,
          loss: 34000,
          roi: 8.2
        },
        status: 'pending'
      }];
      setCompanies(mockCompanies);
    } catch (error) {
      console.error('Failed to fetch companies:', error);
    } finally {
      setLoading(false);
    }
  };
  const getCompanyById = (id: string): Company | undefined => {
    return companies.find(company => company.id === id);
  };
  return <CompanyContext.Provider value={{
    companies,
    loading,
    fetchCompanies,
    getCompanyById
  }}>
      {children}
    </CompanyContext.Provider>;
};