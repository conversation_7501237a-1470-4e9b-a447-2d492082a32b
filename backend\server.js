/**
 * Production-Ready Express Server
 * Main server file with comprehensive middleware and error handling
 */

import express from 'express';
import http from 'http';
import path from 'path';
import { fileURLToPath } from 'url';
import swaggerUi from 'swagger-ui-express';

// Configuration and utilities
import { config } from './config/environment.js';
import { connectDatabase } from './config/database.js';
import logger, { requestLogger, errorLogger } from './utils/logger.js';
import { errorHandler, notFoundHandler } from './utils/errors.js';
import { applySecurity } from './middleware/security.js';
import { performanceMiddleware, initializePerformanceMonitoring } from './utils/performance.js';

// Routes
import apiRoutes from './routes/index.js';

// WebSocket
import webSocketManager from './websocket/server.js';

// Swagger documentation
import swaggerSpecs from './docs/swagger.js';

// Get current directory
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

/**
 * Create Express application
 */
const app = express();
const server = http.createServer(app);

/**
 * Trust proxy for accurate IP addresses
 */
app.set('trust proxy', config.TRUST_PROXY);

/**
 * Apply security middleware
 */
applySecurity(app);

/**
 * Body parsing middleware
 */
app.use(express.json({
  limit: config.MAX_FILE_SIZE || '10mb',
  strict: true
}));

app.use(express.urlencoded({
  extended: true,
  limit: config.MAX_FILE_SIZE || '10mb'
}));

/**
 * Performance monitoring
 */
app.use(performanceMiddleware());

/**
 * Request logging
 */
app.use(requestLogger);

/**
 * Static file serving
 */
app.use('/uploads', express.static(path.join(__dirname, 'uploads')));
app.use('/public', express.static(path.join(__dirname, 'public')));

/**
 * API Documentation
 */
if (config.NODE_ENV !== 'production' || config.FEATURES.API_DOCS) {
  app.use('/api-docs', swaggerUi.serve, swaggerUi.setup(swaggerSpecs, {
    explorer: true,
    customCss: '.swagger-ui .topbar { display: none }',
    customSiteTitle: 'Investment Management API Documentation'
  }));
}

/**
 * API Routes
 */
app.use('/api', apiRoutes);

/**
 * Health check endpoint (before 404 handler)
 */
app.get('/health', (req, res) => {
  res.json({
    success: true,
    message: 'Server is healthy',
    timestamp: new Date().toISOString(),
    version: config.APP_VERSION,
    environment: config.NODE_ENV,
    uptime: process.uptime(),
    memory: process.memoryUsage()
  });
});

/**
 * Root endpoint
 */
app.get('/', (req, res) => {
  res.json({
    success: true,
    message: 'Investment Management System API',
    version: config.APP_VERSION,
    documentation: config.NODE_ENV !== 'production' ? '/api-docs' : null,
    endpoints: {
      health: '/health',
      api: '/api',
      websocket: '/ws'
    }
  });
});

/**
 * 404 handler for undefined routes
 */
app.use(notFoundHandler);

/**
 * Error logging middleware
 */
app.use(errorLogger);

/**
 * Global error handler
 */
app.use(errorHandler);

/**
 * Graceful shutdown handling
 */
const gracefulShutdown = (signal) => {
  logger.info(`Received ${signal}. Starting graceful shutdown...`);

  server.close((err) => {
    if (err) {
      logger.error('Error during server shutdown:', err);
      process.exit(1);
    }

    logger.info('HTTP server closed');

    // Close WebSocket connections
    webSocketManager.close();

    // Close database connection
    import('mongoose').then(mongoose => {
      mongoose.connection.close(() => {
        logger.info('Database connection closed');
        process.exit(0);
      });
    });
  });

  // Force shutdown after 30 seconds
  setTimeout(() => {
    logger.error('Forced shutdown after timeout');
    process.exit(1);
  }, 30000);
};

/**
 * Process event handlers
 */
process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
process.on('SIGINT', () => gracefulShutdown('SIGINT'));

process.on('uncaughtException', (err) => {
  logger.error('Uncaught Exception:', err);
  gracefulShutdown('uncaughtException');
});

process.on('unhandledRejection', (reason, promise) => {
  logger.error('Unhandled Rejection at:', promise, 'reason:', reason);
  gracefulShutdown('unhandledRejection');
});

/**
 * Start server
 */
const startServer = async () => {
  try {
    // Initialize performance monitoring
    initializePerformanceMonitoring();

    // Connect to database
    await connectDatabase();
    logger.info('✅ Database connected successfully');

    // Initialize WebSocket server
    webSocketManager.initialize(server);
    logger.info('✅ WebSocket server initialized');

    // Start HTTP server
    server.listen(config.PORT, config.HOST, () => {
      logger.info(`🚀 Server running on ${config.HOST}:${config.PORT}`);
      logger.info(`📝 Environment: ${config.NODE_ENV}`);
      logger.info(`📚 API Documentation: http://${config.HOST}:${config.PORT}/api-docs`);
      logger.info(`🔌 WebSocket endpoint: ws://${config.HOST}:${config.PORT}/ws`);

      // Log feature flags
      logger.info('🎛️  Feature flags:', {
        rateLimiting: config.FEATURES.RATE_LIMITING,
        realTimeUpdates: config.FEATURES.REAL_TIME_UPDATES,
        auditLogging: config.FEATURES.AUDIT_LOGGING,
        apiDocs: config.FEATURES.API_DOCS
      });
    });

  } catch (error) {
    logger.error('❌ Failed to start server:', error);
    process.exit(1);
  }
};

/**
 * Start the application
 */
if (import.meta.url === `file://${process.argv[1]}`) {
  startServer();
}

export { app, server };
export default app;
