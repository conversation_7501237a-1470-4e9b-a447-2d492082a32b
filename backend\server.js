import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import rateLimit from 'express-rate-limit';
import dotenv from 'dotenv';
import mongoose from 'mongoose';

// Load environment variables
dotenv.config();

// Simple route handlers (we'll add the full routes later)
const createSimpleRoutes = (app) => {
  // Auth routes
  app.post('/api/auth/login', (req, res) => {
    res.json({ success: true, message: 'Login endpoint ready', data: null });
  });

  app.post('/api/auth/register', (req, res) => {
    res.json({ success: true, message: 'Register endpoint ready', data: null });
  });

  // Users routes
  app.get('/api/users', (req, res) => {
    res.json({ success: true, message: 'Users endpoint ready', data: [] });
  });

  // Companies routes
  app.get('/api/companies', (req, res) => {
    res.json({ success: true, message: 'Companies endpoint ready', data: [] });
  });

  // Investments routes
  app.get('/api/investments', (req, res) => {
    res.json({ success: true, message: 'Investments endpoint ready', data: [] });
  });

  // Analytics routes
  app.get('/api/analytics', (req, res) => {
    res.json({ success: true, message: 'Analytics endpoint ready', data: {} });
  });

  // Assets routes
  app.get('/api/assets', (req, res) => {
    res.json({ success: true, message: 'Assets endpoint ready', data: [] });
  });
};

const app = express();
const PORT = process.env.PORT || 9000;

// Security middleware
app.use(helmet());

// Rate limiting
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // limit each IP to 100 requests per windowMs
  message: 'Too many requests from this IP, please try again later.'
});
app.use('/api/', limiter);

// CORS configuration
app.use(cors({
  origin: process.env.CORS_ORIGIN || 'http://localhost:5174',
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH'],
  allowedHeaders: ['Content-Type', 'Authorization']
}));

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// MongoDB connection
const connectDB = async () => {
  try {
    const mongoURI = process.env.MONGODB_URI || 'mongodb://localhost:27017/investment_management';

    await mongoose.connect(mongoURI, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });

    console.log('✅ MongoDB connected successfully');
    console.log(`📊 Database: ${mongoose.connection.name}`);
  } catch (error) {
    console.error('❌ MongoDB connection error:', error.message);

    // For development, we'll continue without MongoDB and use mock data
    console.log('🔄 Continuing with mock data for development...');
  }
};

// Connect to database
connectDB();

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({
    status: 'OK',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    environment: process.env.NODE_ENV || 'development',
    database: mongoose.connection.readyState === 1 ? 'connected' : 'disconnected'
  });
});

// Create simple API routes
createSimpleRoutes(app);

// Root endpoint
app.get('/', (req, res) => {
  res.json({
    message: '🚀 Investment Management System API',
    version: '1.0.0',
    status: 'running',
    endpoints: {
      health: '/health',
      auth: '/api/auth',
      users: '/api/users',
      companies: '/api/companies',
      investments: '/api/investments',
      analytics: '/api/analytics',
      assets: '/api/assets'
    }
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    error: 'Endpoint not found',
    message: `Cannot ${req.method} ${req.originalUrl}`,
    availableEndpoints: [
      'GET /',
      'GET /health',
      'POST /api/auth/login',
      'GET /api/users',
      'GET /api/companies',
      'GET /api/investments',
      'GET /api/analytics',
      'GET /api/assets'
    ]
  });
});

// Global error handler
app.use((error, req, res, next) => {
  console.error('❌ Server Error:', error);

  res.status(error.status || 500).json({
    error: 'Internal Server Error',
    message: process.env.NODE_ENV === 'development' ? error.message : 'Something went wrong',
    ...(process.env.NODE_ENV === 'development' && { stack: error.stack })
  });
});

// Graceful shutdown
process.on('SIGTERM', async () => {
  console.log('🔄 SIGTERM received, shutting down gracefully...');
  await mongoose.connection.close();
  process.exit(0);
});

process.on('SIGINT', async () => {
  console.log('🔄 SIGINT received, shutting down gracefully...');
  await mongoose.connection.close();
  process.exit(0);
});

// Start server
app.listen(PORT, () => {
  console.log('🚀 Investment Management System Backend');
  console.log('=' .repeat(50));
  console.log(`✅ Server running on port ${PORT}`);
  console.log(`🌐 API URL: http://localhost:${PORT}`);
  console.log(`🔗 Health Check: http://localhost:${PORT}/health`);
  console.log(`📚 API Documentation: http://localhost:${PORT}`);
  console.log('=' .repeat(50));
});

export default app;
