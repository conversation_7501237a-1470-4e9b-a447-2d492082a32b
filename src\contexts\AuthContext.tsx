import React, { useEffect, useState, createContext, useContext } from 'react';
import { useNavigate } from 'react-router-dom';
type UserRole = 'superadmin' | 'admin' | 'investor' | null;
type UserStatus = 'active' | 'pending' | 'rejected' | null;
interface AuthContextType {
  user: {
    role: UserRole;
    companyId?: string;
    status?: UserStatus;
    companyName?: string;
  } | null;
  token: string | null;
  isAuthenticated: boolean;
  login: (email: string, password: string) => Promise<void>;
  logout: () => void;
  isLoading: boolean;
  updateUserStatus: (userId: string, status: UserStatus) => Promise<void>;
}
const AuthContext = createContext<AuthContextType>({
  user: null,
  token: null,
  isAuthenticated: false,
  login: async () => {},
  logout: () => {},
  isLoading: false,
  updateUserStatus: async () => {}
});
export const useAuth = () => useContext(AuthContext);
export const AuthProvider: React.FC<{
  children: React.ReactNode;
}> = ({
  children
}) => {
  const [user, setUser] = useState<{
    role: UserRole;
    companyId?: string;
    status?: UserStatus;
    companyName?: string;
  } | null>(null);
  const [token, setToken] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const navigate = useNavigate();
  // Check for existing session on mount
  useEffect(() => {
    const storedToken = localStorage.getItem('authToken');
    const storedUser = localStorage.getItem('authUser');
    if (storedToken && storedUser) {
      setToken(storedToken);
      setUser(JSON.parse(storedUser));
    }
    setIsLoading(false);
  }, []);
  const login = async (email: string, password: string) => {
    try {
      setIsLoading(true);
      // In a real app, this would be an API call
      // For demo purposes, we'll simulate a successful login
      const mockResponse = {
        role: email.includes('superadmin') ? 'superadmin' : email.includes('admin') ? 'admin' : 'investor',
        companyId: email.includes('superadmin') ? undefined : 'abc123',
        companyName: email.includes('admin') ? 'TechVest Inc.' : undefined,
        accessToken: 'mock-jwt-token',
        // For demo, set investors as pending if they have 'new' in their email
        status: email.includes('investor') && email.includes('new') ? 'pending' : 'active'
      };
      const userData = {
        role: mockResponse.role as UserRole,
        companyId: mockResponse.companyId,
        status: mockResponse.role === 'investor' ? mockResponse.status as UserStatus : undefined,
        companyName: mockResponse.companyName
      };
      setUser(userData);
      setToken(mockResponse.accessToken);
      localStorage.setItem('authToken', mockResponse.accessToken);
      localStorage.setItem('authUser', JSON.stringify(userData));
      // Redirect based on role and status
      if (mockResponse.role === 'superadmin') {
        navigate('/superadmin/dashboard');
      } else if (mockResponse.role === 'admin') {
        navigate('/admin/dashboard');
      } else if (mockResponse.role === 'investor') {
        if (mockResponse.status === 'pending') {
          navigate('/investor/pending');
        } else {
          navigate('/investor/dashboard');
        }
      }
    } catch (error) {
      console.error('Login failed:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };
  const logout = () => {
    setUser(null);
    setToken(null);
    localStorage.removeItem('authToken');
    localStorage.removeItem('authUser');
    navigate('/login');
  };
  const updateUserStatus = async (userId: string, status: UserStatus) => {
    try {
      setIsLoading(true);
      // In a real app, this would be an API call
      // For demo, we'll just simulate a delay
      await new Promise(resolve => setTimeout(resolve, 1000));
      console.log(`User ${userId} status updated to ${status}`);
      // If updating the current user's status
      if (user && user.role === 'investor') {
        const updatedUser = {
          ...user,
          status
        };
        setUser(updatedUser);
        localStorage.setItem('authUser', JSON.stringify(updatedUser));
        // Redirect if necessary
        if (status === 'active') {
          navigate('/investor/dashboard');
        } else if (status === 'rejected') {
          logout();
        }
      }
      return true;
    } catch (error) {
      console.error('Failed to update user status:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };
  return <AuthContext.Provider value={{
    user,
    token,
    isAuthenticated: !!token,
    login,
    logout,
    isLoading,
    updateUserStatus
  }}>
      {children}
    </AuthContext.Provider>;
};