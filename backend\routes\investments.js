import express from 'express';
import Investment from '../models/Investment.js';
import Asset from '../models/Asset.js';
import { SubCompany } from '../models/Company.js';
import { authMiddleware, requireRole } from '../middleware/auth.js';

const router = express.Router();

// Get all investments
router.get('/', authMiddleware, async (req, res) => {
  try {
    const { subCompanyId, status, riskLevel } = req.query;
    
    // Build filter
    const filter = {};
    if (subCompanyId) filter.subCompanyId = subCompanyId;
    if (status) filter.status = status;
    if (riskLevel) filter.riskLevel = riskLevel;

    const investments = await Investment.find(filter)
      .populate('assetId')
      .populate('subCompanyId')
      .sort({ createdAt: -1 });

    // Transform data to match frontend expectations
    const transformedInvestments = investments.map(investment => ({
      id: investment._id,
      sub_company_id: investment.subCompanyId._id,
      asset_id: investment.assetId._id,
      name: investment.name,
      description: investment.description,
      initial_amount: investment.initialAmount,
      current_value: investment.currentValue,
      min_investment: investment.minInvestment,
      max_investment: investment.maxInvestment,
      expected_roi: investment.expectedROI,
      actual_roi: investment.actualROI,
      start_date: investment.startDate,
      end_date: investment.endDate,
      status: investment.status,
      risk_level: investment.riskLevel,
      category: investment.category,
      tags: investment.tags,
      is_public: investment.isPublic,
      featured: investment.featured,
      created_at: investment.createdAt,
      updated_at: investment.updatedAt,
      asset: {
        id: investment.assetId._id,
        name: investment.assetId.name,
        type: investment.assetId.type,
        symbol: investment.assetId.symbol,
        description: investment.assetId.description,
        current_price: investment.assetId.currentPrice,
        price_change_24h: investment.assetId.priceChange24h,
        price_change_percentage: investment.assetId.priceChangePercentage,
        logo: investment.assetId.logo,
        created_at: investment.assetId.createdAt,
        updated_at: investment.assetId.updatedAt
      },
      subCompany: {
        id: investment.subCompanyId._id,
        name: investment.subCompanyId.name,
        industry: investment.subCompanyId.industry,
        status: investment.subCompanyId.status
      },
      investorInvestments: [], // TODO: Populate if needed
      profitLossRecords: [],   // TODO: Populate if needed
      totalInvested: investment.performanceMetrics.totalInvested,
      totalInvestors: investment.performanceMetrics.totalInvestors,
      currentROI: investment.currentROI
    }));

    res.json({
      success: true,
      data: transformedInvestments
    });

  } catch (error) {
    console.error('Get investments error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

// Get investment by ID
router.get('/:id', authMiddleware, async (req, res) => {
  try {
    const investment = await Investment.findById(req.params.id)
      .populate('assetId')
      .populate('subCompanyId');

    if (!investment) {
      return res.status(404).json({
        success: false,
        message: 'Investment not found'
      });
    }

    const transformedInvestment = {
      id: investment._id,
      sub_company_id: investment.subCompanyId._id,
      asset_id: investment.assetId._id,
      name: investment.name,
      description: investment.description,
      initial_amount: investment.initialAmount,
      current_value: investment.currentValue,
      min_investment: investment.minInvestment,
      max_investment: investment.maxInvestment,
      expected_roi: investment.expectedROI,
      actual_roi: investment.actualROI,
      start_date: investment.startDate,
      end_date: investment.endDate,
      status: investment.status,
      risk_level: investment.riskLevel,
      category: investment.category,
      tags: investment.tags,
      is_public: investment.isPublic,
      featured: investment.featured,
      created_at: investment.createdAt,
      updated_at: investment.updatedAt,
      asset: {
        id: investment.assetId._id,
        name: investment.assetId.name,
        type: investment.assetId.type,
        symbol: investment.assetId.symbol,
        description: investment.assetId.description,
        current_price: investment.assetId.currentPrice,
        price_change_24h: investment.assetId.priceChange24h,
        price_change_percentage: investment.assetId.priceChangePercentage,
        logo: investment.assetId.logo,
        created_at: investment.assetId.createdAt,
        updated_at: investment.assetId.updatedAt
      },
      subCompany: {
        id: investment.subCompanyId._id,
        name: investment.subCompanyId.name,
        industry: investment.subCompanyId.industry,
        status: investment.subCompanyId.status
      },
      totalInvested: investment.performanceMetrics.totalInvested,
      totalInvestors: investment.performanceMetrics.totalInvestors,
      currentROI: investment.currentROI
    };

    res.json({
      success: true,
      data: transformedInvestment
    });

  } catch (error) {
    console.error('Get investment error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

// Create investment
router.post('/', authMiddleware, requireRole(['superadmin', 'admin']), async (req, res) => {
  try {
    const {
      subCompanyId,
      assetId,
      name,
      description,
      initialAmount,
      minInvestment,
      maxInvestment,
      expectedROI,
      riskLevel,
      category,
      tags
    } = req.body;

    // Validate sub-company exists
    const subCompany = await SubCompany.findById(subCompanyId);
    if (!subCompany) {
      return res.status(404).json({
        success: false,
        message: 'Sub-company not found'
      });
    }

    // Validate asset exists
    const asset = await Asset.findById(assetId);
    if (!asset) {
      return res.status(404).json({
        success: false,
        message: 'Asset not found'
      });
    }

    // Create investment
    const investment = new Investment({
      subCompanyId,
      assetId,
      name,
      description,
      initialAmount,
      currentValue: initialAmount, // Start with initial amount
      minInvestment,
      maxInvestment,
      expectedROI,
      startDate: new Date(),
      status: 'Active',
      riskLevel,
      category,
      tags: tags || []
    });

    await investment.save();
    await investment.populate(['assetId', 'subCompanyId']);

    const transformedInvestment = {
      id: investment._id,
      sub_company_id: investment.subCompanyId._id,
      asset_id: investment.assetId._id,
      name: investment.name,
      description: investment.description,
      initial_amount: investment.initialAmount,
      current_value: investment.currentValue,
      min_investment: investment.minInvestment,
      max_investment: investment.maxInvestment,
      expected_roi: investment.expectedROI,
      actual_roi: investment.actualROI,
      start_date: investment.startDate,
      end_date: investment.endDate,
      status: investment.status,
      risk_level: investment.riskLevel,
      category: investment.category,
      tags: investment.tags,
      is_public: investment.isPublic,
      featured: investment.featured,
      created_at: investment.createdAt,
      updated_at: investment.updatedAt,
      asset: {
        id: investment.assetId._id,
        name: investment.assetId.name,
        type: investment.assetId.type,
        symbol: investment.assetId.symbol,
        description: investment.assetId.description,
        current_price: investment.assetId.currentPrice,
        price_change_24h: investment.assetId.priceChange24h,
        price_change_percentage: investment.assetId.priceChangePercentage,
        logo: investment.assetId.logo,
        created_at: investment.assetId.createdAt,
        updated_at: investment.assetId.updatedAt
      },
      subCompany: {
        id: investment.subCompanyId._id,
        name: investment.subCompanyId.name,
        industry: investment.subCompanyId.industry,
        status: investment.subCompanyId.status
      },
      totalInvested: investment.performanceMetrics.totalInvested,
      totalInvestors: investment.performanceMetrics.totalInvestors,
      currentROI: investment.currentROI
    };

    res.status(201).json({
      success: true,
      message: 'Investment created successfully',
      data: transformedInvestment
    });

  } catch (error) {
    console.error('Create investment error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

// Update investment
router.put('/:id', authMiddleware, requireRole(['superadmin', 'admin']), async (req, res) => {
  try {
    const {
      name,
      description,
      currentValue,
      minInvestment,
      maxInvestment,
      expectedROI,
      status,
      riskLevel,
      category,
      tags
    } = req.body;

    const investment = await Investment.findById(req.params.id);
    
    if (!investment) {
      return res.status(404).json({
        success: false,
        message: 'Investment not found'
      });
    }

    // Update fields
    if (name) investment.name = name;
    if (description) investment.description = description;
    if (currentValue !== undefined) investment.currentValue = currentValue;
    if (minInvestment !== undefined) investment.minInvestment = minInvestment;
    if (maxInvestment !== undefined) investment.maxInvestment = maxInvestment;
    if (expectedROI !== undefined) investment.expectedROI = expectedROI;
    if (status) investment.status = status;
    if (riskLevel) investment.riskLevel = riskLevel;
    if (category) investment.category = category;
    if (tags) investment.tags = tags;

    await investment.save();
    await investment.populate(['assetId', 'subCompanyId']);

    const transformedInvestment = {
      id: investment._id,
      sub_company_id: investment.subCompanyId._id,
      asset_id: investment.assetId._id,
      name: investment.name,
      description: investment.description,
      initial_amount: investment.initialAmount,
      current_value: investment.currentValue,
      min_investment: investment.minInvestment,
      max_investment: investment.maxInvestment,
      expected_roi: investment.expectedROI,
      actual_roi: investment.actualROI,
      start_date: investment.startDate,
      end_date: investment.endDate,
      status: investment.status,
      risk_level: investment.riskLevel,
      category: investment.category,
      tags: investment.tags,
      is_public: investment.isPublic,
      featured: investment.featured,
      created_at: investment.createdAt,
      updated_at: investment.updatedAt,
      asset: {
        id: investment.assetId._id,
        name: investment.assetId.name,
        type: investment.assetId.type,
        symbol: investment.assetId.symbol,
        description: investment.assetId.description,
        current_price: investment.assetId.currentPrice,
        price_change_24h: investment.assetId.priceChange24h,
        price_change_percentage: investment.assetId.priceChangePercentage,
        logo: investment.assetId.logo,
        created_at: investment.assetId.createdAt,
        updated_at: investment.assetId.updatedAt
      },
      subCompany: {
        id: investment.subCompanyId._id,
        name: investment.subCompanyId.name,
        industry: investment.subCompanyId.industry,
        status: investment.subCompanyId.status
      },
      totalInvested: investment.performanceMetrics.totalInvested,
      totalInvestors: investment.performanceMetrics.totalInvestors,
      currentROI: investment.currentROI
    };

    res.json({
      success: true,
      message: 'Investment updated successfully',
      data: transformedInvestment
    });

  } catch (error) {
    console.error('Update investment error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

// Delete investment
router.delete('/:id', authMiddleware, requireRole(['superadmin', 'admin']), async (req, res) => {
  try {
    const investment = await Investment.findById(req.params.id);
    
    if (!investment) {
      return res.status(404).json({
        success: false,
        message: 'Investment not found'
      });
    }

    await Investment.findByIdAndDelete(req.params.id);

    res.json({
      success: true,
      message: 'Investment deleted successfully'
    });

  } catch (error) {
    console.error('Delete investment error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

export default router;
